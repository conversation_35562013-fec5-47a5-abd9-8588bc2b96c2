import React, { useEffect, useRef, useState, useContext, useCallback } from 'react';
import ChatHistory from './ChatHistory';
import VoiceInput from './VoiceInput';
import ActivationStatus from '../ui/ActivationStatus';
import { useStreamingChat } from '../../hooks/useStreamingChat';
import { ToastContext } from '../../contexts/toast'; // 导入Toast上下文
import { useTheme } from '../../contexts/theme';

interface VoiceChatPanelProps {
  isVoiceChatMode: boolean;
  onClose: () => void;
}

const VoiceChatPanel: React.FC<VoiceChatPanelProps> = ({
  isVoiceChatMode,
  onClose
}) => {
  const { theme } = useTheme();

  // 检查应用激活状态
  const [isActivated, setIsActivated] = useState<boolean>(false);
  const [voiceProvider, setVoiceProvider] = useState<'tongyi-gummy'>('tongyi-gummy');
  const [language, setLanguage] = useState<string>('zh'); // 语音识别语言
  const [microphonePermissionGranted, setMicrophonePermissionGranted] = useState<boolean>(false);
  const [systemAudioPermissionGranted, setSystemAudioPermissionGranted] = useState<boolean>(false);
  const [listeningMode, setListeningMode] = useState<'microphone-only' | 'system-only' | 'dual-audio'>('microphone-only'); // 监听模式
  const toast = useContext(ToastContext); // 使用Toast上下文
  const contentRef = useRef<HTMLDivElement>(null); // 添加内容引用
  const [voiceConnectionStatus, setVoiceConnectionStatus] = useState<boolean>(false); // 语音识别连接状态
  
  // 使用流式聊天hook
  const {
    messages,
    isStreaming,
    sendMessage,
    stopStreaming,
    error,
    setMessages
  } = useStreamingChat();

  // 检查麦克风权限
  const checkMicrophonePermission = async (): Promise<boolean> => {
    try {
      console.log('检查麦克风权限...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      console.log('麦克风权限检查成功');
      return true;
    } catch (error) {
      console.error('麦克风权限被拒绝:', error);
      if (toast) {
        toast.showToast("麦克风权限被拒绝", "请允许访问麦克风以使用语音功能", "error");
      }
      return false;
    }
  };

  // 检查系统音频权限
  const checkSystemAudioPermission = async (): Promise<boolean> => {
    try {
      console.log('检查系统音频权限...');
      if (!window.electronAPI?.checkSystemAudioPermission) {
        console.warn('系统音频权限检查API不可用');
        return false;
      }

      const hasPermission = await window.electronAPI.checkSystemAudioPermission();
      setSystemAudioPermissionGranted(hasPermission);

      if (!hasPermission) {
        if (toast) {
          toast.showToast("系统音频权限未授予", "请在系统设置中启用立体声混音功能", "error");
        }
      }

      return hasPermission;
    } catch (error) {
      console.error('检查系统音频权限失败:', error);
      if (toast) {
        toast.showToast("系统音频权限检查失败", "无法检查系统音频权限", "error");
      }
      return false;
    }
  };

  // 预初始化系统音频捕获（性能优化）
  const preInitializeSystemAudio = useCallback(async () => {
    try {
      if (window.electronAPI?.preInitializeSystemAudio) {
        console.log('VoiceChatPanel: 开始系统音频预初始化...');

        // 并行执行主进程和渲染进程的预初始化
        const initTasks = [
          window.electronAPI.preInitializeSystemAudio({
            sampleRate: 16000,
            channels: 1,
            bufferSize: 4096
          }),
          // 预初始化渲染进程
          window.systemAudioCapture?.preInitialize?.() || Promise.resolve()
        ];

        await Promise.allSettled(initTasks);
        console.log('VoiceChatPanel: 系统音频预初始化完成');
      }
    } catch (error) {
      console.warn('VoiceChatPanel: 预初始化系统音频失败:', error);
    }
  }, []);

  // 检查应用激活状态
  useEffect(() => {
    const checkActivation = async () => {
      try {
        const result = await window.electronAPI.checkActivation();
        setIsActivated(result.activated);
      } catch (err) {
        console.error('检查激活状态失败:', err);
        setIsActivated(false);
      }
    };
    
    checkActivation();
    
    // 监听激活状态变化
    const unsubscribeActivationSuccess = window.electronAPI.onActivationSuccess(() => {
      setIsActivated(true);
    });
    
    const unsubscribeActivationRequired = window.electronAPI.onActivationRequired(() => {
      setIsActivated(false);
    });
    
    return () => {
      unsubscribeActivationSuccess();
      unsubscribeActivationRequired();
    };
  }, []);

  // 加载语音提供商配置、语言设置和音频监听模式
  useEffect(() => {
    const loadVoiceConfig = async () => {
      try {
        if (window.electronAPI && window.electronAPI.getVoiceConfig) {
          const config = await window.electronAPI.getVoiceConfig();
          if (config) {
            console.log('加载语音配置:', config);
            if (config.selectedProvider) {
              setVoiceProvider(config.selectedProvider as 'tongyi-gummy');
            }
            if (config.language) {
              setLanguage(config.language);
            }
            if (config.audioListeningMode) {
              setListeningMode(config.audioListeningMode);
            }
          }
        }
      } catch (error) {
        console.warn('无法加载语音配置:', error);
      }
    };

    // 初始加载配置
    loadVoiceConfig();

    // 监听语音配置更新事件
    const unsubscribeVoiceConfigUpdated = window.electronAPI.onVoiceConfigUpdated?.((updatedConfig: any) => {
      console.log('收到语音配置更新事件:', updatedConfig);
      if (updatedConfig) {
        if (updatedConfig.selectedProvider) {
          setVoiceProvider(updatedConfig.selectedProvider as 'tongyi-gummy');
        }
        if (updatedConfig.language) {
          setLanguage(updatedConfig.language);
        }
        if (updatedConfig.audioListeningMode) {
          setListeningMode(updatedConfig.audioListeningMode);
        }
      }
    });

    // 清理函数
    return () => {
      if (unsubscribeVoiceConfigUpdated) {
        unsubscribeVoiceConfigUpdated();
      }
    };
  }, []);

  // 管理语音识别连接和权限检查（进入/退出语音聊天模式时）
  useEffect(() => {
    if (isVoiceChatMode) {
      // 进入语音聊天模式，检查相关权限
      const initializeVoiceMode = async () => {
        console.log('进入语音聊天模式，检查权限...');

        // 检查麦克风权限（如果需要麦克风）
        if (listeningMode === 'microphone-only' || listeningMode === 'dual-audio') {
          const hasMicPermission = await checkMicrophonePermission();
          setMicrophonePermissionGranted(hasMicPermission);

          if (!hasMicPermission && listeningMode === 'microphone-only') {
            console.error('麦克风权限检查失败，无法进入语音模式');
            return;
          }
        }

        // 检查系统音频权限（如果需要系统音频）
        if (listeningMode === 'system-only' || listeningMode === 'dual-audio') {
          const hasSystemAudioPermission = await checkSystemAudioPermission();
          setSystemAudioPermissionGranted(hasSystemAudioPermission);

          if (!hasSystemAudioPermission && listeningMode === 'system-only') {
            console.error('系统音频权限检查失败，无法进入语音模式');
            return;
          }

          // 如果系统音频权限检查通过，执行预初始化
          if (hasSystemAudioPermission) {
            console.log('开始系统音频预初始化...');
            await preInitializeSystemAudio();
          }
        }

        console.log('权限检查完成');
        // 语音识别连接管理现在由 VoiceInput 组件负责
      };

      initializeVoiceMode();
    } else {
      // 退出语音聊天模式，清理资源
      console.log('退出语音聊天模式，清理系统音频缓存...');

      // 清除系统音频缓存
      if (window.electronAPI?.clearSystemAudioCache) {
        window.electronAPI.clearSystemAudioCache().catch((error: any) => {
          console.warn('清除系统音频缓存失败:', error);
        });
      }

      setMicrophonePermissionGranted(false);
      setSystemAudioPermissionGranted(false);
      setVoiceConnectionStatus(false);
    }
  }, [isVoiceChatMode, voiceProvider, listeningMode, toast, preInitializeSystemAudio]);

  // 处理语音识别连接状态变化
  const handleVoiceConnectionStatusChange = useCallback((isConnected: boolean) => {
    console.log('VoiceChatPanel: 语音识别连接状态变化:', isConnected);
    setVoiceConnectionStatus(isConnected);
  }, []);

  // 添加重置语音聊天功能
  useEffect(() => {
    if (!isVoiceChatMode) return;
    
    // 处理重置语音聊天事件
    const handleResetVoiceChat = () => {
      console.log("收到重置语音聊天历史事件...");
      // 停止任何正在进行的流
      if (isStreaming) {
        console.log("停止正在进行的流式传输...");
        stopStreaming();
      }
      // 清空聊天记录
      console.log("清空聊天历史...");
      setMessages([]);
      
      // 使用Toast提示用户对话已重置
      if (toast) {
        toast.showToast("对话已重置", "可以开始新的对话了", "success");
      }
    };
    
    // 监听重置事件
    console.log("注册reset-voice-chat事件监听器");
    window.electronAPI.on("reset-voice-chat", handleResetVoiceChat);
    
    // 清理函数
    return () => {
      console.log("移除reset-voice-chat事件监听器");
      window.electronAPI.removeListener("reset-voice-chat", handleResetVoiceChat);
    };
  }, [isVoiceChatMode, isStreaming, stopStreaming, setMessages, toast]);
  
  // 监听键盘快捷键
  useEffect(() => {
    if (!isVoiceChatMode) return;
    
    // 处理全局键盘事件
    const handleKeyDown = (e: KeyboardEvent) => {
      // Esc键关闭语音面板
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    // 清理函数
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      // 停止任何正在进行的流式传输
      if (isStreaming) {
        stopStreaming();
      }
    };
  }, [isVoiceChatMode, onClose, isStreaming, stopStreaming]);

  // 添加尺寸更新逻辑，优化响应速度和稳定性平衡
  useEffect(() => {
    if (!isVoiceChatMode || !contentRef.current) return;

    let updateTimeout: number | null = null;
    let lastDimensions = { width: 0, height: 0 };
    let isInitialized = false;
    let lastUpdateTime = 0;

    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = Math.max(contentRef.current.scrollHeight, 600); // 设置最小高度为600
        const contentWidth = Math.max(contentRef.current.scrollWidth, 750);  // 设置最小宽度为750

        // 只有当尺寸变化显著时才更新（防止微小变化导致抖动）
        const widthDiff = Math.abs(contentWidth - lastDimensions.width);
        const heightDiff = Math.abs(contentHeight - lastDimensions.height);

        if (!isInitialized || widthDiff > 8 || heightDiff > 8) { // 降低阈值从10px到8px，提高敏感度
          lastDimensions = { width: contentWidth, height: contentHeight };
          isInitialized = true;
          lastUpdateTime = Date.now();

          window.electronAPI.updateContentDimensions({
            width: contentWidth,
            height: contentHeight
          });
        }
      }
    };

    const smartUpdateDimensions = () => {
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }

      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdateTime;

      // 如果距离上次更新时间较短，使用较短的防抖时间（快速响应）
      // 如果距离上次更新时间较长，使用较长的防抖时间（防止抖动）
      const debounceTime = timeSinceLastUpdate < 500 ? 80 : 150; // 80ms快速响应，150ms稳定防抖

      updateTimeout = setTimeout(updateDimensions, debounceTime) as unknown as number;
    };

    // 初始化尺寸更新（不防抖）
    updateDimensions();

    // 设置ResizeObserver监听尺寸变化（使用智能防抖）
    const resizeObserver = new ResizeObserver(smartUpdateDimensions);
    resizeObserver.observe(contentRef.current);

    // 延迟更新以确保内容完全加载
    const delayedUpdate = setTimeout(updateDimensions, 1000) as unknown as number;

    return () => {
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }
      resizeObserver.disconnect();
      clearTimeout(delayedUpdate);
    };
  }, [isVoiceChatMode]);

  // 条件渲染前的最终检查
  if (!isVoiceChatMode || !isActivated) {
    return null;
  }
  
  return (
    <div
      ref={contentRef}
      className="voice-chat-panel fixed inset-0 z-50 flex flex-col min-h-[600px] min-w-[750px]"
      style={{
        backgroundColor: theme === 'dark' ? 'rgba(0, 0, 0, 0.85)' : 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(10px)',
        borderRadius: '12px',
        boxShadow: theme === 'dark' ? '0 0 20px rgba(0, 0, 0, 0.5)' : '0 0 20px rgba(0, 0, 0, 0.2)',
        maxHeight: '100vh',
        maxWidth: '100vw'
      }}
    >
      <div
        className="p-4 flex justify-between items-center flex-shrink-0 min-h-[72px]"
        style={{
          backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.7)' : 'rgba(245, 245, 245, 0.7)',
          backdropFilter: 'blur(8px)',
          borderBottom: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
        }}
      >
        <div className="flex items-center gap-4 flex-1">
          <h2
            className="text-xl font-semibold flex-shrink-0"
            style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
          >
            语音对话
          </h2>
          {/* Activation Status */}
          <div className="flex-shrink-0">
            <ActivationStatus />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div
            className="text-sm flex gap-4"
            style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)' }}
          >
            <span>按 Cmd/Ctrl+Shift+M 退出语音模式</span>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-md transition-colors"
            style={{
              color: theme === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            title="关闭语音对话"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {error && (
        <div
          className="p-2 text-center"
          style={{
            backgroundColor: theme === 'dark' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)',
            color: theme === 'dark' ? '#fca5a5' : '#dc2626',
            borderBottom: `1px solid ${theme === 'dark' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.2)'}`
          }}
        >
          {error}
        </div>
      )}

      {/* 麦克风权限检查失败提示 */}
      {!microphonePermissionGranted && isVoiceChatMode && (listeningMode === 'microphone-only' || listeningMode === 'dual-audio') && (
        <div
          className="p-4 text-center"
          style={{
            backgroundColor: theme === 'dark' ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.1)',
            color: theme === 'dark' ? '#fbbf24' : '#d97706',
            borderBottom: `1px solid ${theme === 'dark' ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`
          }}
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="font-semibold">麦克风权限未授权</span>
          </div>
          <p className="text-sm">请允许访问麦克风以使用语音功能。如果已授权但仍显示此提示，请刷新页面重试。</p>
        </div>
      )}

      {/* 系统音频权限检查失败提示 */}
      {!systemAudioPermissionGranted && isVoiceChatMode && (listeningMode === 'system-only' || listeningMode === 'dual-audio') && (
        <div
          className="p-4 text-center"
          style={{
            backgroundColor: theme === 'dark' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)',
            color: theme === 'dark' ? '#f87171' : '#dc2626',
            borderBottom: `1px solid ${theme === 'dark' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.2)'}`
          }}
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="font-semibold">系统音频权限未授权</span>
          </div>
          <p className="text-sm">请在系统设置中启用"立体声混音"功能，或检查系统音频捕获权限。</p>
        </div>
      )}

      {/* 快捷键提示区域 */}
      <div
        className="px-4 py-2 flex justify-between items-center flex-shrink-0 min-h-[40px]"
        style={{
          backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.4)' : 'rgba(245, 245, 245, 0.4)',
          backdropFilter: 'blur(6px)',
          borderBottom: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
        }}
      >
        {/* 当前监听模式显示 */}
        <div className="flex items-center gap-2">
          <span
            className="text-xs"
            style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)' }}
          >
            当前模式：
          </span>
          <div className="flex items-center gap-1">
            {listeningMode === 'microphone-only' && (
              <span
                className="text-xs"
                style={{ color: theme === 'dark' ? 'rgba(59, 130, 246, 0.8)' : 'rgba(37, 99, 235, 0.8)' }}
              >
                🎤 仅麦克风
              </span>
            )}
            {listeningMode === 'system-only' && (
              <span
                className="text-xs"
                style={{ color: theme === 'dark' ? 'rgba(245, 158, 11, 0.8)' : 'rgba(217, 119, 6, 0.8)' }}
              >
                🔊 仅系统音频
              </span>
            )}
            {listeningMode === 'dual-audio' && (
              <span
                className="text-xs"
                style={{ color: theme === 'dark' ? 'rgba(16, 185, 129, 0.8)' : 'rgba(5, 150, 105, 0.8)' }}
              >
                🎧 双音频源
              </span>
            )}
          </div>
        </div>

        {/* 快捷键提示 */}
        <span
          className="text-xs"
          style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)' }}
        >
          按 Cmd/Ctrl+R 清空重置对话
        </span>
      </div>

      {/* 聊天历史 */}
      <ChatHistory messages={messages} />

      {/* 语音输入控制 */}
      <VoiceInput
        onSendMessage={sendMessage}
        isStreaming={isStreaming}
        listeningMode={listeningMode}
        onConnectionStatusChange={handleVoiceConnectionStatusChange}
      />
    </div>
  );
};

export default VoiceChatPanel; 
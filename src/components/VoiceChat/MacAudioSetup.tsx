import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/theme';

interface MacAudioSetupProps {
  onClose: () => void;
}

interface AudioConfiguration {
  hasVirtualDevice: boolean;
  currentOutputDevice: string;
  recommendations: string[];
  isBlackHoleConfigured: boolean;
  systemAudioRouting: string;
}

const MacAudioSetup: React.FC<MacAudioSetupProps> = ({ onClose }) => {
  const { theme } = useTheme();
  const [audioConfig, setAudioConfig] = useState<AudioConfiguration | null>(null);
  const [virtualDevices, setVirtualDevices] = useState<any[]>([]);
  const [setupGuide, setSetupGuide] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAudioConfiguration();
  }, []);

  const loadAudioConfiguration = async () => {
    try {
      setIsLoading(true);
      
      const [config, devices, guide] = await Promise.all([
        window.electronAPI.macCheckAudioConfiguration(),
        window.electronAPI.macDetectVirtualAudioDevices(),
        window.electronAPI.macGetSetupGuide()
      ]);

      setAudioConfig(config);
      setVirtualDevices(devices);
      setSetupGuide(guide);
    } catch (error) {
      console.error('加载Mac音频配置失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenAudioMIDI = async () => {
    try {
      const success = await window.electronAPI.macOpenAudioMidiSetup();
      if (success) {
        console.log('已打开音频MIDI设置');
      }
    } catch (error) {
      console.error('打开音频MIDI设置失败:', error);
    }
  };

  const handleOpenSoundPreferences = async () => {
    try {
      const success = await window.electronAPI.macOpenSoundPreferences();
      if (success) {
        console.log('已打开系统声音偏好设置');
      }
    } catch (error) {
      console.error('打开系统声音偏好设置失败:', error);
    }
  };

  const handleRefresh = () => {
    loadAudioConfiguration();
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">检测Mac音频配置中...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Mac系统音频设置
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 当前状态 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">当前状态</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <span className="text-sm text-gray-600 dark:text-gray-300 w-32">虚拟音频设备:</span>
              <span className={`text-sm font-medium ${audioConfig?.hasVirtualDevice ? 'text-green-600' : 'text-red-600'}`}>
                {audioConfig?.hasVirtualDevice ? '已安装' : '未安装'}
              </span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600 dark:text-gray-300 w-32">当前输出设备:</span>
              <span className="text-sm text-gray-900 dark:text-white">
                {audioConfig?.currentOutputDevice || '未知'}
              </span>
            </div>
          </div>
        </div>

        {/* 检测到的虚拟设备 */}
        {virtualDevices.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">检测到的虚拟音频设备</h3>
            <div className="space-y-2">
              {virtualDevices.map((device, index) => (
                <div key={index} className="flex items-center p-2 bg-green-50 dark:bg-green-900/20 rounded">
                  <span className="text-sm text-green-700 dark:text-green-300">
                    ✓ {device.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 建议 */}
        {audioConfig?.recommendations && audioConfig.recommendations.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">建议</h3>
            <div className="space-y-2">
              {audioConfig.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start">
                  <span className="text-sm text-gray-600 dark:text-gray-300">• {recommendation}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 设置指南 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">详细设置指南</h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-4 text-sm font-mono">
            {setupGuide.map((line, index) => (
              <div key={index} className="text-gray-700 dark:text-gray-300">
                {line}
              </div>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleOpenAudioMIDI}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            打开音频MIDI设置
          </button>
          <button
            onClick={handleOpenSoundPreferences}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            打开系统声音设置
          </button>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            刷新检测
          </button>
          <button
            onClick={() => window.electronAPI.openExternalUrl?.('https://github.com/ExistentialAudio/BlackHole')}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
          >
            下载BlackHole
          </button>
        </div>

        {/* 关闭按钮 */}
        <div className="mt-6 text-center">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default MacAudioSetup;

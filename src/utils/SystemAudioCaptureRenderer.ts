/**
 * 渲染进程系统音频捕获模块
 * 负责在渲染进程中创建系统音频流并处理音频数据
 * 重构后与useGummySpeechRecognition统一架构兼容
 */

export interface SystemAudioCaptureOptions {
  sampleRate?: number;
  channels?: number;
  bufferSize?: number;
}

// 音频数据回调类型
export type AudioDataCallback = (audioData: ArrayBuffer) => void;

export class SystemAudioCaptureRenderer {
  private audioStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private isCapturing: boolean = false;
  private options: Required<SystemAudioCaptureOptions>;
  private audioDataCallback: AudioDataCallback | null = null;

  // 预初始化状态
  private isPreInitialized: boolean = false;
  private lastSourceId: string | null = null;
  private preInitPromise: Promise<void> | null = null; // 防止重复预初始化

  constructor() {
    this.options = {
      sampleRate: 16000,
      channels: 1,
      bufferSize: 4096
    };
  }

  /**
   * 设置音频数据回调函数
   */
  setAudioDataCallback(callback: AudioDataCallback | null): void {
    this.audioDataCallback = callback;
  }

  /**
   * 预初始化音频上下文（性能优化）
   */
  async preInitialize(): Promise<void> {
    // 如果已经预初始化或正在预初始化，直接返回
    if (this.isPreInitialized) return;
    if (this.preInitPromise) return this.preInitPromise;

    // 创建预初始化 Promise
    this.preInitPromise = this.doPreInitialize();
    return this.preInitPromise;
  }

  /**
   * 执行实际的预初始化工作
   */
  private async doPreInitialize(): Promise<void> {
    try {
      console.log('开始渲染进程音频预初始化...');

      // 预创建音频上下文
      if (!this.audioContext) {
        this.audioContext = new AudioContext({
          sampleRate: this.options.sampleRate
        });

        // 如果音频上下文被暂停，立即恢复
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
        }

        console.log('音频上下文预初始化完成，状态:', this.audioContext.state);
      }

      this.isPreInitialized = true;
      console.log('渲染进程音频预初始化完成');
    } catch (error) {
      console.warn('渲染进程音频预初始化失败:', error);
    } finally {
      this.preInitPromise = null;
    }
  }

  /**
   * 开始捕获系统音频（优化版本，增强调试）
   */
  async startCapture(sourceId: string, options?: SystemAudioCaptureOptions): Promise<boolean> {
    try {
      console.log('渲染进程开始系统音频捕获');
      console.log('源ID:', sourceId);
      console.log('平台:', navigator.platform);
      console.log('用户代理:', navigator.userAgent);

      // 更新配置
      if (options) {
        this.options = { ...this.options, ...options };
        console.log('使用配置:', this.options);
      }

      // 如果是相同的源ID且已经在捕获，直接返回成功
      if (this.isCapturing && this.lastSourceId === sourceId) {
        console.log('使用相同音频源，无需重新初始化');
        return true;
      }

      // 如果正在捕获不同的源，先停止
      if (this.isCapturing && this.lastSourceId !== sourceId) {
        console.log('停止当前捕获，切换到新音频源');
        this.stopCapture();
      }

      // 确保预初始化已完成
      await this.preInitialize();

      // 首先获取所有可用音频源进行调试
      try {
        const availableSources = await this.getAllAvailableSources();
        console.log('当前可用音频源:', availableSources);

        // 验证请求的源ID是否在可用列表中
        const sourceExists = availableSources.some(s => s.id === sourceId);
        console.log('请求的源ID是否存在:', sourceExists);

        if (!sourceExists && availableSources.length > 0) {
          console.warn('请求的源ID不存在，将使用第一个可用源');
          sourceId = availableSources[0].id;
        }
      } catch (sourceError) {
        console.warn('获取音频源列表失败:', sourceError);
      }

      // 并行执行：获取媒体流和准备音频上下文
      console.log('开始获取媒体流...');
      const [audioStream] = await Promise.all([
        this.getMediaStream(sourceId),
        this.ensureAudioContext()
      ]);

      this.audioStream = audioStream;
      this.lastSourceId = sourceId;

      // 检查音频轨道
      const audioTracks = this.audioStream.getAudioTracks();
      console.log('音频轨道详情:');
      audioTracks.forEach((track, index) => {
        console.log(`  轨道 ${index + 1}: ${track.label}, 状态: ${track.readyState}, 启用: ${track.enabled}`);
      });

      if (audioTracks.length === 0) {
        throw new Error('未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log('成功获取系统音频流，音频轨道数量:', audioTracks.length);

      // 设置音频处理管道
      await this.setupAudioPipeline();

      this.isCapturing = true;
      console.log('系统音频捕获已启动');

      return true;
    } catch (error) {
      console.error('启动系统音频捕获失败:', error);
      console.error('错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      this.cleanup();
      return false;
    }
  }

  /**
   * 获取媒体流（修复设备未找到问题）
   */
  private async getMediaStream(sourceId: string): Promise<MediaStream> {
    console.log('尝试获取媒体流，源ID:', sourceId);

    // 检测平台
    const isMac = navigator.platform.toLowerCase().includes('mac');

    // 首先尝试使用兼容性更好的约束格式
    const constraints = {
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          googEchoCancellation: false,
          googAutoGainControl: false,
          googNoiseSuppression: false,
          googHighpassFilter: false
        }
      },
      video: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId,
          maxWidth: 1280,
          maxHeight: 720,
          maxFrameRate: 1
        }
      }
    };

    try {
      console.log(`尝试获取媒体流 [${isMac ? 'Mac' : 'Windows'}平台]`);
      const stream = await navigator.mediaDevices.getUserMedia(constraints as any);

      // 验证音频轨道
      const audioTracks = stream.getAudioTracks();
      console.log(`获取到音频轨道数量: ${audioTracks.length}`);

      if (audioTracks.length === 0) {
        throw new Error(isMac
          ? '未找到音频轨道。请确保在"系统偏好设置 > 安全性与隐私 > 屏幕录制"中授予应用权限。'
          : '未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log(`成功获取媒体流，音频轨道: ${audioTracks.map(t => t.label).join(', ')}`);
      return stream;
    } catch (error) {
      console.error('获取媒体流失败:', error);

      // 如果第一次尝试失败，尝试不同的约束格式
      if (error instanceof Error && error.name === 'NotFoundError') {
        console.log('尝试使用备用约束格式...');
        return this.tryAlternativeConstraints(sourceId, isMac);
      }

      // 处理其他错误
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError' || error.message.includes('Permission denied')) {
          throw new Error(isMac
            ? 'Mac系统需要屏幕录制权限才能捕获系统音频。请前往"系统偏好设置 > 安全性与隐私 > 屏幕录制"，勾选本应用并重启应用。'
            : '系统音频权限被拒绝，请检查浏览器权限设置。');
        }
        if (error.name === 'NotFoundError') {
          throw new Error('未找到指定的音频设备。请确保系统有音频输出设备并且正在播放音频。');
        }
      }

      throw error;
    }
  }

  /**
   * 尝试备用约束格式
   */
  private async tryAlternativeConstraints(sourceId: string, isMac: boolean): Promise<MediaStream> {
    console.log('使用备用约束格式尝试获取媒体流...');

    // 尝试只请求音频
    const audioOnlyConstraints = {
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId
        }
      }
    };

    try {
      const stream = await navigator.mediaDevices.getUserMedia(audioOnlyConstraints as any);
      console.log('备用约束格式成功获取媒体流');
      return stream;
    } catch (error) {
      console.error('备用约束格式也失败:', error);

      // 最后尝试：获取所有可用的音频源并选择第一个
      try {
        console.log('尝试获取所有可用音频源...');
        const sources = await this.getAllAvailableSources();

        if (sources.length > 0) {
          const firstSource = sources[0];
          console.log('使用第一个可用音频源:', firstSource.name);

          const fallbackConstraints = {
            audio: {
              mandatory: {
                chromeMediaSource: 'desktop',
                chromeMediaSourceId: firstSource.id
              }
            },
            video: {
              mandatory: {
                chromeMediaSource: 'desktop',
                chromeMediaSourceId: firstSource.id,
                maxWidth: 640,
                maxHeight: 480,
                maxFrameRate: 1
              }
            }
          };

          return await navigator.mediaDevices.getUserMedia(fallbackConstraints as any);
        }
      } catch (fallbackError) {
        console.error('备用方案也失败:', fallbackError);
      }

      throw new Error(isMac
        ? 'Mac系统无法获取音频流。请确保已授予屏幕录制权限并重启应用。'
        : '无法获取系统音频流。请检查系统音频设置和权限。');
    }
  }

  /**
   * 获取所有可用的音频源
   */
  private async getAllAvailableSources(): Promise<Array<{id: string, name: string}>> {
    try {
      // 通过IPC获取音频源列表
      if (window.electronAPI?.getSystemAudioSources) {
        return await window.electronAPI.getSystemAudioSources();
      }
      return [];
    } catch (error) {
      console.error('获取音频源列表失败:', error);
      return [];
    }
  }

  /**
   * 确保音频上下文准备就绪
   */
  private async ensureAudioContext(): Promise<void> {
    if (!this.audioContext) {
      this.audioContext = new AudioContext({
        sampleRate: this.options.sampleRate
      });
    }

    // 如果音频上下文被暂停，恢复它
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  /**
   * 设置音频处理管道
   */
  private async setupAudioPipeline(): Promise<void> {
    if (!this.audioContext || !this.audioStream) return;

    // 创建音频源
    const source = this.audioContext.createMediaStreamSource(this.audioStream);

    // 创建脚本处理器
    this.scriptProcessor = this.audioContext.createScriptProcessor(
      this.options.bufferSize,
      this.options.channels,
      this.options.channels
    );

    // 处理音频数据
    this.scriptProcessor.onaudioprocess = (event) => {
      if (!this.isCapturing) return;

      const inputBuffer = event.inputBuffer.getChannelData(0);

      // 转换为16位PCM
      const pcmData = new Int16Array(inputBuffer.length);
      for (let i = 0; i < inputBuffer.length; i++) {
        const sample = Math.max(-1, Math.min(1, inputBuffer[i]));
        pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      }

      // 通过回调传递音频数据
      if (this.audioDataCallback) {
        try {
          this.audioDataCallback(pcmData.buffer);
        } catch (error) {
          console.error('音频数据回调执行失败:', error);
        }
      }
    };

    // 连接音频节点
    source.connect(this.scriptProcessor);
    this.scriptProcessor.connect(this.audioContext.destination);
  }

  /**
   * 停止捕获系统音频
   */
  stopCapture(): void {
    try {
      console.log('停止系统音频捕获');
      this.isCapturing = false;
      this.audioDataCallback = null; // 清除回调
      this.cleanup();
    } catch (error) {
      console.error('停止系统音频捕获失败:', error);
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => {
        track.stop();
      });
      this.audioStream = null;
    }
  }





  /**
   * 检查是否正在捕获
   */
  isActive(): boolean {
    return this.isCapturing;
  }

  /**
   * 获取当前配置
   */
  getOptions(): Required<SystemAudioCaptureOptions> {
    return { ...this.options };
  }
}

// 创建全局实例
const systemAudioCaptureRenderer = new SystemAudioCaptureRenderer();

// 将实例暴露到全局对象（类型声明在useGummySpeechRecognition.ts中）

window.systemAudioCapture = systemAudioCaptureRenderer;

export default systemAudioCaptureRenderer;

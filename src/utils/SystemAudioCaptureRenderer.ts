/**
 * 渲染进程系统音频捕获模块
 * 负责在渲染进程中创建系统音频流并处理音频数据
 * 重构后与useGummySpeechRecognition统一架构兼容
 */

export interface SystemAudioCaptureOptions {
  sampleRate?: number;
  channels?: number;
  bufferSize?: number;
}

// 音频数据回调类型
export type AudioDataCallback = (audioData: ArrayBuffer) => void;

export class SystemAudioCaptureRenderer {
  private audioStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private isCapturing: boolean = false;
  private options: Required<SystemAudioCaptureOptions>;
  private audioDataCallback: AudioDataCallback | null = null;

  // 预初始化状态
  private isPreInitialized: boolean = false;
  private lastSourceId: string | null = null;
  private preInitPromise: Promise<void> | null = null; // 防止重复预初始化

  constructor() {
    this.options = {
      sampleRate: 16000,
      channels: 1,
      bufferSize: 4096
    };
  }

  /**
   * 设置音频数据回调函数
   */
  setAudioDataCallback(callback: AudioDataCallback | null): void {
    this.audioDataCallback = callback;
  }

  /**
   * 预初始化音频上下文（性能优化）
   */
  async preInitialize(): Promise<void> {
    // 如果已经预初始化或正在预初始化，直接返回
    if (this.isPreInitialized) return;
    if (this.preInitPromise) return this.preInitPromise;

    // 创建预初始化 Promise
    this.preInitPromise = this.doPreInitialize();
    return this.preInitPromise;
  }

  /**
   * 执行实际的预初始化工作
   */
  private async doPreInitialize(): Promise<void> {
    try {
      console.log('开始渲染进程音频预初始化...');

      // 预创建音频上下文
      if (!this.audioContext) {
        this.audioContext = new AudioContext({
          sampleRate: this.options.sampleRate
        });

        // 如果音频上下文被暂停，立即恢复
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
        }

        console.log('音频上下文预初始化完成，状态:', this.audioContext.state);
      }

      this.isPreInitialized = true;
      console.log('渲染进程音频预初始化完成');
    } catch (error) {
      console.warn('渲染进程音频预初始化失败:', error);
    } finally {
      this.preInitPromise = null;
    }
  }

  /**
   * 开始捕获系统音频（优化版本，增强调试）
   */
  async startCapture(sourceId: string, options?: SystemAudioCaptureOptions): Promise<boolean> {
    try {
      console.log('渲染进程开始系统音频捕获');
      console.log('源ID:', sourceId);
      console.log('平台:', navigator.platform);
      console.log('用户代理:', navigator.userAgent);

      // 更新配置
      if (options) {
        this.options = { ...this.options, ...options };
        console.log('使用配置:', this.options);
      }

      // 如果是相同的源ID且已经在捕获，直接返回成功
      if (this.isCapturing && this.lastSourceId === sourceId) {
        console.log('使用相同音频源，无需重新初始化');
        return true;
      }

      // 如果正在捕获不同的源，先停止
      if (this.isCapturing && this.lastSourceId !== sourceId) {
        console.log('停止当前捕获，切换到新音频源');
        this.stopCapture();
      }

      // 确保预初始化已完成
      await this.preInitialize();

      // 首先获取所有可用音频源进行调试
      try {
        const availableSources = await this.getAllAvailableSources();
        console.log('当前可用音频源:', availableSources);

        // 验证请求的源ID是否在可用列表中
        const sourceExists = availableSources.some(s => s.id === sourceId);
        console.log('请求的源ID是否存在:', sourceExists);

        if (!sourceExists && availableSources.length > 0) {
          console.warn('请求的源ID不存在，将使用第一个可用源');
          sourceId = availableSources[0].id;
        }
      } catch (sourceError) {
        console.warn('获取音频源列表失败:', sourceError);
      }

      // 并行执行：获取媒体流和准备音频上下文
      console.log('开始获取媒体流...');
      const [audioStream] = await Promise.all([
        this.getMediaStream(sourceId),
        this.ensureAudioContext()
      ]);

      this.audioStream = audioStream;
      this.lastSourceId = sourceId;

      // 检查音频轨道
      const audioTracks = this.audioStream.getAudioTracks();
      console.log('音频轨道详情:');
      audioTracks.forEach((track, index) => {
        console.log(`  轨道 ${index + 1}: ${track.label}, 状态: ${track.readyState}, 启用: ${track.enabled}`);
      });

      if (audioTracks.length === 0) {
        throw new Error('未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log('成功获取系统音频流，音频轨道数量:', audioTracks.length);

      // 设置音频处理管道
      await this.setupAudioPipeline();

      this.isCapturing = true;
      console.log('系统音频捕获已启动');

      return true;
    } catch (error) {
      console.error('启动系统音频捕获失败:', error);
      console.error('错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      this.cleanup();
      return false;
    }
  }

  /**
   * 获取媒体流（Mac BlackHole优化版本）
   */
  private async getMediaStream(sourceId: string): Promise<MediaStream> {
    console.log('尝试获取媒体流，源ID:', sourceId);

    // 检测平台
    const isMac = navigator.platform.toLowerCase().includes('mac');

    if (isMac) {
      // Mac平台特殊处理：尝试多种方式获取系统音频
      console.log('Mac平台：尝试获取系统音频流...');

      // 方法1：尝试直接使用屏幕捕获（可能包含BlackHole音频）
      try {
        const stream = await this.tryMacScreenCapture(sourceId);
        if (stream) {
          console.log('Mac屏幕捕获成功');
          return stream;
        }
      } catch (error) {
        console.warn('Mac屏幕捕获失败:', error);
      }

      // 方法2：尝试使用音频输入设备（如果BlackHole配置为输入）
      try {
        const stream = await this.tryMacAudioInput();
        if (stream) {
          console.log('Mac音频输入捕获成功');
          return stream;
        }
      } catch (error) {
        console.warn('Mac音频输入捕获失败:', error);
      }

      // 方法3：回退到标准屏幕捕获
      console.log('Mac平台回退到标准屏幕捕获...');
    }

    // 标准约束格式
    const constraints = {
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          googEchoCancellation: false,
          googAutoGainControl: false,
          googNoiseSuppression: false,
          googHighpassFilter: false
        }
      },
      video: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId,
          maxWidth: 1280,
          maxHeight: 720,
          maxFrameRate: 1
        }
      }
    };

    try {
      console.log(`尝试标准媒体流获取 [${isMac ? 'Mac' : 'Windows'}平台]`);
      const stream = await navigator.mediaDevices.getUserMedia(constraints as any);

      // 验证音频轨道
      const audioTracks = stream.getAudioTracks();
      console.log(`获取到音频轨道数量: ${audioTracks.length}`);

      if (audioTracks.length === 0) {
        throw new Error(isMac
          ? '未找到音频轨道。请确保BlackHole已正确配置并设置为系统音频输出。'
          : '未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log(`成功获取媒体流，音频轨道: ${audioTracks.map(t => t.label).join(', ')}`);
      return stream;
    } catch (error) {
      console.error('获取媒体流失败:', error);

      // 如果第一次尝试失败，尝试不同的约束格式
      if (error instanceof Error && error.name === 'NotFoundError') {
        console.log('尝试使用备用约束格式...');
        return this.tryAlternativeConstraints(sourceId, isMac);
      }

      // 处理其他错误
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError' || error.message.includes('Permission denied')) {
          throw new Error(isMac
            ? 'Mac系统需要屏幕录制权限才能捕获系统音频。请前往"系统偏好设置 > 安全性与隐私 > 屏幕录制"，勾选本应用并重启应用。'
            : '系统音频权限被拒绝，请检查浏览器权限设置。');
        }
        if (error.name === 'NotFoundError') {
          throw new Error(isMac
            ? '未找到音频设备。请确保BlackHole已安装并正确配置为多输出设备。'
            : '未找到指定的音频设备。请确保系统有音频输出设备并且正在播放音频。');
        }
      }

      throw error;
    }
  }

  /**
   * Mac平台屏幕捕获尝试
   */
  private async tryMacScreenCapture(sourceId: string): Promise<MediaStream | null> {
    try {
      const constraints = {
        audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: sourceId,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        },
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: sourceId,
            maxWidth: 640,
            maxHeight: 480,
            maxFrameRate: 1
          }
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints as any);
      const audioTracks = stream.getAudioTracks();

      if (audioTracks.length > 0) {
        console.log('Mac屏幕捕获获得音频轨道:', audioTracks[0].label);
        return stream;
      }

      return null;
    } catch (error) {
      console.warn('Mac屏幕捕获失败:', error);
      return null;
    }
  }

  /**
   * Mac平台音频输入尝试（BlackHole作为输入设备）
   */
  private async tryMacAudioInput(): Promise<MediaStream | null> {
    try {
      // 获取所有音频输入设备
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');

      console.log('可用音频输入设备:', audioInputs.map(d => d.label || d.deviceId));

      // 查找BlackHole设备
      const blackHoleDevice = audioInputs.find(device =>
        device.label.toLowerCase().includes('blackhole') ||
        device.label.toLowerCase().includes('multi-output')
      );

      if (blackHoleDevice) {
        console.log('找到BlackHole输入设备:', blackHoleDevice.label);

        const constraints = {
          audio: {
            deviceId: { exact: blackHoleDevice.deviceId },
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: 48000,
            channelCount: 2
          }
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('成功通过BlackHole输入设备获取音频流');
        return stream;
      }

      return null;
    } catch (error) {
      console.warn('Mac音频输入尝试失败:', error);
      return null;
    }
  }

  /**
   * 尝试备用约束格式
   */
  private async tryAlternativeConstraints(sourceId: string, isMac: boolean): Promise<MediaStream> {
    console.log('使用备用约束格式尝试获取媒体流...');

    // 尝试只请求音频
    const audioOnlyConstraints = {
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sourceId
        }
      }
    };

    try {
      const stream = await navigator.mediaDevices.getUserMedia(audioOnlyConstraints as any);
      console.log('备用约束格式成功获取媒体流');
      return stream;
    } catch (error) {
      console.error('备用约束格式也失败:', error);

      // 最后尝试：获取所有可用的音频源并选择第一个
      try {
        console.log('尝试获取所有可用音频源...');
        const sources = await this.getAllAvailableSources();

        if (sources.length > 0) {
          const firstSource = sources[0];
          console.log('使用第一个可用音频源:', firstSource.name);

          const fallbackConstraints = {
            audio: {
              mandatory: {
                chromeMediaSource: 'desktop',
                chromeMediaSourceId: firstSource.id
              }
            },
            video: {
              mandatory: {
                chromeMediaSource: 'desktop',
                chromeMediaSourceId: firstSource.id,
                maxWidth: 640,
                maxHeight: 480,
                maxFrameRate: 1
              }
            }
          };

          return await navigator.mediaDevices.getUserMedia(fallbackConstraints as any);
        }
      } catch (fallbackError) {
        console.error('备用方案也失败:', fallbackError);
      }

      throw new Error(isMac
        ? 'Mac系统无法获取音频流。请确保已授予屏幕录制权限并重启应用。'
        : '无法获取系统音频流。请检查系统音频设置和权限。');
    }
  }

  /**
   * 获取所有可用的音频源
   */
  private async getAllAvailableSources(): Promise<Array<{id: string, name: string}>> {
    try {
      // 通过IPC获取音频源列表
      if (window.electronAPI?.getSystemAudioSources) {
        return await window.electronAPI.getSystemAudioSources();
      }
      return [];
    } catch (error) {
      console.error('获取音频源列表失败:', error);
      return [];
    }
  }

  /**
   * 确保音频上下文准备就绪
   */
  private async ensureAudioContext(): Promise<void> {
    if (!this.audioContext) {
      this.audioContext = new AudioContext({
        sampleRate: this.options.sampleRate
      });
    }

    // 如果音频上下文被暂停，恢复它
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  /**
   * 设置音频处理管道（增强Mac音频检测）
   */
  private async setupAudioPipeline(): Promise<void> {
    if (!this.audioContext || !this.audioStream) return;

    const isMac = navigator.platform.toLowerCase().includes('mac');

    // 创建音频源
    const source = this.audioContext.createMediaStreamSource(this.audioStream);

    // 创建脚本处理器
    this.scriptProcessor = this.audioContext.createScriptProcessor(
      this.options.bufferSize,
      this.options.channels,
      this.options.channels
    );

    // Mac平台音频检测和处理
    let audioDataCount = 0;
    let silentSampleCount = 0;
    let lastAudioCheck = Date.now();
    let totalAudioSent = 0;
    let lastValidAudioTime = Date.now();

    // 处理音频数据
    this.scriptProcessor.onaudioprocess = (event) => {
      if (!this.isCapturing) return;

      const inputBuffer = event.inputBuffer.getChannelData(0);
      audioDataCount++;

      // 检测音频数据是否有效（非静音）
      let hasAudio = false;
      let maxAmplitude = 0;
      let rmsLevel = 0;
      let nonZeroSamples = 0;

      // 更精确的音频检测
      for (let i = 0; i < inputBuffer.length; i++) {
        const amplitude = Math.abs(inputBuffer[i]);
        maxAmplitude = Math.max(maxAmplitude, amplitude);
        rmsLevel += amplitude * amplitude;

        if (amplitude > 0.0001) { // 降低阈值，更敏感
          hasAudio = true;
          nonZeroSamples++;
        }
      }

      rmsLevel = Math.sqrt(rmsLevel / inputBuffer.length);

      if (hasAudio) {
        silentSampleCount = 0;
        lastValidAudioTime = Date.now();
      } else {
        silentSampleCount++;
      }

      // Mac平台特殊检测和调试
      if (isMac && audioDataCount % 50 === 0) { // 每50个样本检查一次，更频繁
        const now = Date.now();
        if (now - lastAudioCheck > 3000) { // 3秒检查一次
          console.log(`Mac音频详细状态:`);
          console.log(`- 最大振幅: ${maxAmplitude.toFixed(8)}`);
          console.log(`- RMS电平: ${rmsLevel.toFixed(8)}`);
          console.log(`- 非零样本: ${nonZeroSamples}/${inputBuffer.length}`);
          console.log(`- 静音样本计数: ${silentSampleCount}`);
          console.log(`- 总样本: ${audioDataCount}`);
          console.log(`- 已发送音频数据: ${totalAudioSent} 次`);
          console.log(`- 上次有效音频: ${now - lastValidAudioTime}ms 前`);

          if (silentSampleCount > 150) { // 降低阈值，更早警告
            console.warn('⚠️ Mac系统音频长时间静音，可能的问题：');
            console.warn('1. 系统没有播放音频');
            console.warn('2. BlackHole未正确配置');
            console.warn('3. 系统音频输出未设置为多输出设备');
            console.warn('4. 多输出设备中未包含BlackHole');

            // 尝试获取当前音频配置信息
            if (window.electronAPI?.macCheckAudioConfiguration) {
              window.electronAPI.macCheckAudioConfiguration().then((config: any) => {
                console.log('当前音频配置:', config);
              }).catch((err: any) => {
                console.warn('获取音频配置失败:', err);
              });
            }
          }

          lastAudioCheck = now;
        }
      }

      // 转换为16位PCM
      const pcmData = new Int16Array(inputBuffer.length);
      for (let i = 0; i < inputBuffer.length; i++) {
        const sample = Math.max(-1, Math.min(1, inputBuffer[i]));
        pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      }

      // 通过回调传递音频数据（即使是静音也要发送，让语音识别服务判断）
      if (this.audioDataCallback) {
        try {
          this.audioDataCallback(pcmData.buffer);
          totalAudioSent++;

          // 如果是Mac且有音频数据，记录成功
          if (isMac && hasAudio && totalAudioSent % 100 === 0) {
            console.log(`✅ Mac音频数据传输正常，已发送 ${totalAudioSent} 个音频包`);
          }
        } catch (error) {
          console.error('音频数据回调执行失败:', error);
        }
      }
    };

    // 连接音频节点
    source.connect(this.scriptProcessor);
    this.scriptProcessor.connect(this.audioContext.destination);

    console.log(`音频处理管道已设置 [${isMac ? 'Mac' : 'Other'}平台]`);
  }

  /**
   * 停止捕获系统音频
   */
  stopCapture(): void {
    try {
      console.log('停止系统音频捕获');
      this.isCapturing = false;
      this.audioDataCallback = null; // 清除回调
      this.cleanup();
    } catch (error) {
      console.error('停止系统音频捕获失败:', error);
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => {
        track.stop();
      });
      this.audioStream = null;
    }
  }





  /**
   * 检查是否正在捕获
   */
  isActive(): boolean {
    return this.isCapturing;
  }

  /**
   * 获取当前配置
   */
  getOptions(): Required<SystemAudioCaptureOptions> {
    return { ...this.options };
  }
}

// 创建全局实例
const systemAudioCaptureRenderer = new SystemAudioCaptureRenderer();

// 将实例暴露到全局对象（类型声明在useGummySpeechRecognition.ts中）

window.systemAudioCapture = systemAudioCaptureRenderer;

export default systemAudioCaptureRenderer;

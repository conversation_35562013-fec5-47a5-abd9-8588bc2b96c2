/**
 * 渲染进程系统音频捕获模块
 * 负责在渲染进程中创建系统音频流并处理音频数据
 * 重构后与useGummySpeechRecognition统一架构兼容
 */

export interface SystemAudioCaptureOptions {
  sampleRate?: number;
  channels?: number;
  bufferSize?: number;
}

// 音频数据回调类型
export type AudioDataCallback = (audioData: ArrayBuffer) => void;

export class SystemAudioCaptureRenderer {
  private audioStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private isCapturing: boolean = false;
  private options: Required<SystemAudioCaptureOptions>;
  private audioDataCallback: AudioDataCallback | null = null;

  // 预初始化状态
  private isPreInitialized: boolean = false;
  private lastSourceId: string | null = null;
  private preInitPromise: Promise<void> | null = null; // 防止重复预初始化

  constructor() {
    this.options = {
      sampleRate: 16000,
      channels: 1,
      bufferSize: 4096
    };
  }

  /**
   * 设置音频数据回调函数
   */
  setAudioDataCallback(callback: AudioDataCallback | null): void {
    this.audioDataCallback = callback;
  }

  /**
   * 预初始化音频上下文（性能优化）
   */
  async preInitialize(): Promise<void> {
    // 如果已经预初始化或正在预初始化，直接返回
    if (this.isPreInitialized) return;
    if (this.preInitPromise) return this.preInitPromise;

    // 创建预初始化 Promise
    this.preInitPromise = this.doPreInitialize();
    return this.preInitPromise;
  }

  /**
   * 执行实际的预初始化工作
   */
  private async doPreInitialize(): Promise<void> {
    try {
      console.log('开始渲染进程音频预初始化...');

      // 预创建音频上下文
      if (!this.audioContext) {
        this.audioContext = new AudioContext({
          sampleRate: this.options.sampleRate
        });

        // 如果音频上下文被暂停，立即恢复
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
        }

        console.log('音频上下文预初始化完成，状态:', this.audioContext.state);
      }

      this.isPreInitialized = true;
      console.log('渲染进程音频预初始化完成');
    } catch (error) {
      console.warn('渲染进程音频预初始化失败:', error);
    } finally {
      this.preInitPromise = null;
    }
  }

  /**
   * 开始捕获系统音频（优化版本）
   */
  async startCapture(sourceId: string, options?: SystemAudioCaptureOptions): Promise<boolean> {
    try {
      console.log('渲染进程开始系统音频捕获，源ID:', sourceId);

      // 更新配置
      if (options) {
        this.options = { ...this.options, ...options };
      }

      // 如果是相同的源ID且已经在捕获，直接返回成功
      if (this.isCapturing && this.lastSourceId === sourceId) {
        console.log('使用相同音频源，无需重新初始化');
        return true;
      }

      // 如果正在捕获不同的源，先停止
      if (this.isCapturing && this.lastSourceId !== sourceId) {
        this.stopCapture();
      }

      // 确保预初始化已完成
      await this.preInitialize();

      // 并行执行：获取媒体流和准备音频上下文
      const [audioStream] = await Promise.all([
        this.getMediaStream(sourceId),
        this.ensureAudioContext()
      ]);

      this.audioStream = audioStream;
      this.lastSourceId = sourceId;

      // 检查音频轨道
      const audioTracks = this.audioStream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error('未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log('成功获取系统音频流，音频轨道数量:', audioTracks.length);

      // 设置音频处理管道
      await this.setupAudioPipeline();

      this.isCapturing = true;
      console.log('系统音频捕获已启动');

      return true;
    } catch (error) {
      console.error('启动系统音频捕获失败:', error);
      this.cleanup();
      return false;
    }
  }

  /**
   * 获取媒体流（支持Mac平台优化）
   */
  private async getMediaStream(sourceId: string): Promise<MediaStream> {
    // 检测平台
    const isMac = navigator.platform.toLowerCase().includes('mac');

    // 使用标准约束格式（替代废弃的mandatory格式）
    const constraints: MediaStreamConstraints = {
      audio: {
        // 标准约束格式
        echoCancellation: false,
        noiseSuppression: false,
        autoGainControl: false,
        // Chrome特定约束
        // @ts-ignore - Electron/Chrome特定属性
        chromeMediaSource: 'desktop',
        // @ts-ignore - Electron/Chrome特定属性
        chromeMediaSourceId: sourceId,
        // Mac特定优化
        ...(isMac && {
          // @ts-ignore - Mac特定音频约束
          sampleRate: { ideal: 48000, min: 16000 },
          // @ts-ignore - Mac特定音频约束
          channelCount: { ideal: 2, min: 1 }
        })
      },
      video: {
        // 即使只需要音频，也需要请求视频权限（Electron要求）
        width: { ideal: 1280, max: 1920 },
        height: { ideal: 720, max: 1080 },
        frameRate: { ideal: 1, max: 5 },
        // @ts-ignore - Electron/Chrome特定属性
        chromeMediaSource: 'desktop',
        // @ts-ignore - Electron/Chrome特定属性
        chromeMediaSourceId: sourceId
      }
    };

    try {
      console.log(`获取媒体流 [${isMac ? 'Mac' : 'Other'}平台]，源ID:`, sourceId);
      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      // 验证音频轨道
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error(isMac
          ? '未找到音频轨道。请确保在"系统偏好设置 > 安全性与隐私 > 屏幕录制"中授予应用权限。'
          : '未找到音频轨道，可能需要在系统设置中启用"立体声混音"或类似选项');
      }

      console.log(`成功获取媒体流，音频轨道数量: ${audioTracks.length}`);
      return stream;
    } catch (error) {
      console.error('获取媒体流失败:', error);

      // Mac特定错误处理
      if (isMac && error instanceof Error) {
        if (error.name === 'NotAllowedError' || error.message.includes('Permission denied')) {
          throw new Error('Mac系统需要屏幕录制权限才能捕获系统音频。请前往"系统偏好设置 > 安全性与隐私 > 屏幕录制"，勾选本应用并重启应用。');
        }
        if (error.name === 'NotFoundError') {
          throw new Error('未找到可用的音频源。请确保系统有音频输出设备并且正在播放音频。');
        }
      }

      throw error;
    }
  }

  /**
   * 确保音频上下文准备就绪
   */
  private async ensureAudioContext(): Promise<void> {
    if (!this.audioContext) {
      this.audioContext = new AudioContext({
        sampleRate: this.options.sampleRate
      });
    }

    // 如果音频上下文被暂停，恢复它
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  /**
   * 设置音频处理管道
   */
  private async setupAudioPipeline(): Promise<void> {
    if (!this.audioContext || !this.audioStream) return;

    // 创建音频源
    const source = this.audioContext.createMediaStreamSource(this.audioStream);

    // 创建脚本处理器
    this.scriptProcessor = this.audioContext.createScriptProcessor(
      this.options.bufferSize,
      this.options.channels,
      this.options.channels
    );

    // 处理音频数据
    this.scriptProcessor.onaudioprocess = (event) => {
      if (!this.isCapturing) return;

      const inputBuffer = event.inputBuffer.getChannelData(0);

      // 转换为16位PCM
      const pcmData = new Int16Array(inputBuffer.length);
      for (let i = 0; i < inputBuffer.length; i++) {
        const sample = Math.max(-1, Math.min(1, inputBuffer[i]));
        pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      }

      // 通过回调传递音频数据
      if (this.audioDataCallback) {
        try {
          this.audioDataCallback(pcmData.buffer);
        } catch (error) {
          console.error('音频数据回调执行失败:', error);
        }
      }
    };

    // 连接音频节点
    source.connect(this.scriptProcessor);
    this.scriptProcessor.connect(this.audioContext.destination);
  }

  /**
   * 停止捕获系统音频
   */
  stopCapture(): void {
    try {
      console.log('停止系统音频捕获');
      this.isCapturing = false;
      this.audioDataCallback = null; // 清除回调
      this.cleanup();
    } catch (error) {
      console.error('停止系统音频捕获失败:', error);
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => {
        track.stop();
      });
      this.audioStream = null;
    }
  }





  /**
   * 检查是否正在捕获
   */
  isActive(): boolean {
    return this.isCapturing;
  }

  /**
   * 获取当前配置
   */
  getOptions(): Required<SystemAudioCaptureOptions> {
    return { ...this.options };
  }
}

// 创建全局实例
const systemAudioCaptureRenderer = new SystemAudioCaptureRenderer();

// 将实例暴露到全局对象（类型声明在useGummySpeechRecognition.ts中）

window.systemAudioCapture = systemAudioCaptureRenderer;

export default systemAudioCaptureRenderer;

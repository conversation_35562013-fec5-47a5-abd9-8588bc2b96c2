/**
 * 音频启动优化器
 * 负责优化音频捕获的启动性能
 */

export interface AudioStartupConfig {
  enablePreInitialization: boolean;
  enableParallelInitialization: boolean;
  enableCaching: boolean;
  connectionTimeout: number;
  initializationDelay: number;
}

export class AudioStartupOptimizer {
  private static instance: AudioStartupOptimizer | null = null;
  private config: AudioStartupConfig;
  private isOptimized = false;

  private constructor() {
    this.config = {
      enablePreInitialization: true,
      enableParallelInitialization: true,
      enableCaching: true,
      connectionTimeout: 500, // 减少连接超时时间
      initializationDelay: 100 // 减少初始化延迟
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): AudioStartupOptimizer {
    if (!AudioStartupOptimizer.instance) {
      AudioStartupOptimizer.instance = new AudioStartupOptimizer();
    }
    return AudioStartupOptimizer.instance;
  }

  /**
   * 优化音频启动配置
   */
  async optimizeStartup(): Promise<void> {
    if (this.isOptimized) {
      console.log('音频启动已优化，跳过重复优化');
      return;
    }

    try {
      console.log('开始优化音频启动配置...');

      // 并行执行多个优化任务
      const optimizationTasks = [
        this.preWarmAudioContext(),
        this.preloadAudioPermissions(),
        this.optimizeConnectionSettings()
      ];

      await Promise.allSettled(optimizationTasks);

      this.isOptimized = true;
      console.log('音频启动优化完成');
    } catch (error) {
      console.warn('音频启动优化失败:', error);
    }
  }

  /**
   * 预热音频上下文
   */
  private async preWarmAudioContext(): Promise<void> {
    try {
      // 创建一个临时的音频上下文来预热
      const tempContext = new AudioContext({ sampleRate: 16000 });
      
      // 如果被暂停，立即恢复
      if (tempContext.state === 'suspended') {
        await tempContext.resume();
      }
      
      // 创建一个静音的音频节点来初始化音频管道
      const oscillator = tempContext.createOscillator();
      const gainNode = tempContext.createGain();
      
      gainNode.gain.value = 0; // 静音
      oscillator.connect(gainNode);
      gainNode.connect(tempContext.destination);
      
      oscillator.start();
      oscillator.stop(tempContext.currentTime + 0.01); // 10ms后停止
      
      // 短暂延迟后关闭临时上下文
      setTimeout(() => {
        tempContext.close();
      }, 100);
      
      console.log('音频上下文预热完成');
    } catch (error) {
      console.warn('音频上下文预热失败:', error);
    }
  }

  /**
   * 预加载音频权限
   */
  private async preloadAudioPermissions(): Promise<void> {
    try {
      // 检查麦克风权限API可用性
      if (navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function') {
        // 不实际请求权限，只检查API可用性
        console.log('麦克风API可用');
      }

      // 检查系统音频权限
      if (window.electronAPI?.checkSystemAudioPermission) {
        await window.electronAPI.checkSystemAudioPermission();
        console.log('系统音频权限检查完成');
      }

      console.log('音频权限预加载完成');
    } catch (error) {
      console.warn('音频权限预加载失败:', error);
    }
  }

  /**
   * 优化连接设置
   */
  private async optimizeConnectionSettings(): Promise<void> {
    try {
      // 设置优化的连接参数
      if (window.electronAPI?.setAudioOptimizationConfig) {
        await window.electronAPI.setAudioOptimizationConfig({
          bufferSize: 4096, // 优化的缓冲区大小
          sampleRate: 16000, // 标准采样率
          channels: 1, // 单声道
          enableEchoCancellation: false, // 禁用回声消除以提高性能
          enableNoiseSuppression: false // 禁用噪声抑制以提高性能
        });
      }

      console.log('连接设置优化完成');
    } catch (error) {
      console.warn('连接设置优化失败:', error);
    }
  }

  /**
   * 获取优化的启动配置
   */
  getOptimizedConfig(): AudioStartupConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AudioStartupConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('音频启动配置已更新:', this.config);
  }

  /**
   * 重置优化状态
   */
  reset(): void {
    this.isOptimized = false;
    console.log('音频启动优化状态已重置');
  }

  /**
   * 获取性能统计信息
   */
  getPerformanceStats(): {
    isOptimized: boolean;
    config: AudioStartupConfig;
    recommendations: string[];
  } {
    const recommendations: string[] = [];

    if (!this.config.enablePreInitialization) {
      recommendations.push('启用预初始化以提高启动速度');
    }

    if (!this.config.enableParallelInitialization) {
      recommendations.push('启用并行初始化以减少启动时间');
    }

    if (this.config.connectionTimeout > 1000) {
      recommendations.push('减少连接超时时间以提高响应速度');
    }

    return {
      isOptimized: this.isOptimized,
      config: this.config,
      recommendations
    };
  }
}

// 导出单例实例
export const audioStartupOptimizer = AudioStartupOptimizer.getInstance();

// 在模块加载时自动开始优化
if (typeof window !== 'undefined') {
  // 延迟执行优化，避免阻塞主线程
  setTimeout(() => {
    audioStartupOptimizer.optimizeStartup().catch(console.warn);
  }, 100);
}

import { EventEmitter } from 'events';
import { desktopCapturer, BrowserWindow } from 'electron';
import { audioCacheManager } from './AudioCacheManager';

export interface SystemAudioCaptureOptions {
  sampleRate?: number;
  channels?: number;
  bufferSize?: number;
}

export interface SystemAudioSource {
  id: string;
  name: string;
  type: 'screen' | 'window';
}

export class SystemAudioCapture extends EventEmitter {
  private isCapturing: boolean = false;
  private options: Required<SystemAudioCaptureOptions>;
  private mainWindow: BrowserWindow | null = null;
  private selectedSourceId: string | null = null;

  // 缓存相关
  private static audioSourcesCache: SystemAudioSource[] | null = null;
  private static cacheTimestamp: number = 0;
  private static readonly CACHE_DURATION = 2 * 60 * 60 * 1000; // 2小时缓存（接近永久）

  // 预初始化状态
  private static globalPreInitialized: boolean = false;
  private static preInitPromise: Promise<void> | null = null; // 防止重复预初始化

  constructor(options: SystemAudioCaptureOptions = {}, mainWindow?: BrowserWindow) {
    super();
    this.options = {
      sampleRate: options.sampleRate || 16000,
      channels: options.channels || 1,
      bufferSize: options.bufferSize || 4096
    };
    this.mainWindow = mainWindow || null;
  }

  /**
   * 设置主窗口引用
   */
  setMainWindow(mainWindow: BrowserWindow): void {
    this.mainWindow = mainWindow;
  }

  /**
   * 预初始化音频源（可选的性能优化）
   */
  async preInitialize(): Promise<void> {
    // 如果已经预初始化或正在预初始化，直接返回
    if (SystemAudioCapture.globalPreInitialized) return;
    if (SystemAudioCapture.preInitPromise) return SystemAudioCapture.preInitPromise;

    // 创建预初始化 Promise
    SystemAudioCapture.preInitPromise = this.doPreInitialize();
    return SystemAudioCapture.preInitPromise;
  }

  /**
   * 执行实际的预初始化工作
   */
  private async doPreInitialize(): Promise<void> {
    try {
      console.log('开始系统音频捕获预初始化...');

      // 并行执行多个初始化任务
      const initTasks = [
        audioCacheManager.preInitialize(), // 使用缓存管理器预初始化
        this.preInitializeRenderer() // 预初始化渲染进程
      ];

      await Promise.allSettled(initTasks);

      SystemAudioCapture.globalPreInitialized = true;
      console.log('系统音频捕获预初始化完成');
    } catch (error) {
      console.warn('系统音频捕获预初始化失败:', error);
    } finally {
      SystemAudioCapture.preInitPromise = null;
    }
  }

  /**
   * 预初始化渲染进程
   */
  private async preInitializeRenderer(): Promise<void> {
    if (!this.mainWindow) return;

    try {
      // 预初始化渲染进程的音频上下文
      await this.mainWindow.webContents.executeJavaScript(`
        if (window.systemAudioCapture && window.systemAudioCapture.preInitialize) {
          window.systemAudioCapture.preInitialize();
        }
      `);
      console.log('渲染进程音频上下文预初始化完成');
    } catch (error) {
      console.warn('渲染进程预初始化失败:', error);
    }
  }

  /**
   * 开始捕获系统音频（Mac BlackHole优化版本）
   */
  async startCapture(sourceId?: string): Promise<boolean> {
    try {
      console.log('开始捕获系统音频...');

      if (!this.mainWindow) {
        throw new Error('主窗口引用未设置');
      }

      const isMac = process.platform === 'darwin';

      // 如果已经在捕获相同的源，直接返回成功
      if (this.isCapturing && this.selectedSourceId === sourceId) {
        console.log('已在捕获相同音频源，无需重新启动');
        return true;
      }

      // 确保预初始化已完成
      await this.preInitialize();

      // Mac平台特殊处理：优先检测BlackHole设备
      if (isMac) {
        console.log('Mac平台：检测BlackHole音频设备...');

        try {
          // 通知渲染进程检测BlackHole设备
          const blackHoleDetected = await this.mainWindow.webContents.executeJavaScript(`
            (async () => {
              try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                const blackHoleDevice = audioInputs.find(device =>
                  device.label.toLowerCase().includes('blackhole') ||
                  device.label.toLowerCase().includes('black hole')
                );

                if (blackHoleDevice) {
                  console.log('✅ 检测到BlackHole设备:', blackHoleDevice.label);
                  return {
                    found: true,
                    deviceId: blackHoleDevice.deviceId,
                    label: blackHoleDevice.label
                  };
                } else {
                  console.log('❌ 未检测到BlackHole设备');
                  console.log('可用音频输入设备:', audioInputs.map(d => d.label));
                  return { found: false };
                }
              } catch (error) {
                console.error('检测BlackHole设备失败:', error);
                return { found: false, error: error.message };
              }
            })()
          `);

          if (blackHoleDetected.found) {
            console.log('✅ 检测到BlackHole设备，使用直接音频捕获');
            // 使用特殊的BlackHole源ID
            this.selectedSourceId = `blackhole:${blackHoleDetected.deviceId}`;

            // 启动BlackHole直接捕获
            const result = await this.startBlackHoleCapture(blackHoleDetected);
            if (result) {
              this.isCapturing = true;
              console.log('BlackHole音频捕获已开始');
              this.emit('started');
              return true;
            } else {
              console.warn('BlackHole直接捕获失败，回退到屏幕捕获');
            }
          } else {
            console.warn('❌ 未检测到BlackHole设备，使用屏幕捕获方式');
            if (blackHoleDetected.error) {
              console.error('BlackHole检测错误:', blackHoleDetected.error);
            }
          }
        } catch (error) {
          console.error('BlackHole检测过程失败:', error);
        }
      }

      // 回退到传统屏幕捕获方式
      console.log('使用传统屏幕捕获方式...');

      // 并行执行：获取音频源和准备渲染进程
      const [sources] = await Promise.all([
        audioCacheManager.getAudioSources(), // 使用缓存管理器获取音频源
        this.ensureRendererReady()
      ]);

      if (sources.length === 0) {
        throw new Error('未找到可用的音频源。请检查系统权限设置。');
      }

      // 改进音频源选择逻辑
      this.selectedSourceId = await this.selectBestAudioSource(sources, sourceId);
      const selectedSource = sources.find(s => s.id === this.selectedSourceId);
      console.log('使用音频源:', selectedSource?.name || this.selectedSourceId);

      // 验证选择的音频源是否有效
      if (!this.selectedSourceId || !sources.some(s => s.id === this.selectedSourceId)) {
        console.warn('选择的音频源无效，使用第一个可用源');
        this.selectedSourceId = sources[0].id;
      }

      // 优化的IPC通信：使用更直接的方式
      const result = await this.startCaptureInRenderer();

      if (!result) {
        // 如果失败，尝试使用其他音频源
        if (sources.length > 1) {
          console.log('尝试使用备用音频源...');
          let retryResult = false;

          for (let i = 1; i < sources.length; i++) {
            this.selectedSourceId = sources[i].id;
            console.log('尝试音频源:', sources[i].name);
            retryResult = await this.startCaptureInRenderer();
            if (retryResult) {
              console.log('备用音频源启动成功');
              break;
            }
          }

          if (!retryResult) {
            throw new Error('所有音频源都无法启动，请检查系统音频设置和权限');
          }
        } else {
          throw new Error('渲染进程音频捕获启动失败，请检查系统音频设置');
        }
      }

      this.isCapturing = true;
      console.log('系统音频捕获已开始');
      this.emit('started');

      return true;
    } catch (error) {
      console.error('系统音频捕获失败:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * 启动BlackHole直接捕获
   */
  private async startBlackHoleCapture(blackHoleInfo: any): Promise<boolean> {
    try {
      console.log('启动BlackHole直接捕获:', blackHoleInfo.label);

      // 确保渲染进程准备就绪
      await this.ensureRendererReady();

      // 通知渲染进程使用BlackHole设备
      const result = await this.mainWindow!.webContents.executeJavaScript(`
        (async () => {
          try {
            // 获取BlackHole音频流
            const stream = await navigator.mediaDevices.getUserMedia({
              audio: {
                deviceId: { exact: '${blackHoleInfo.deviceId}' },
                echoCancellation: false,
                noiseSuppression: false,
                autoGainControl: false,
                sampleRate: { ideal: 48000, min: 16000 },
                channelCount: { ideal: 2, min: 1 }
              }
            });

            console.log('✅ 成功获取BlackHole音频流');

            // 启动系统音频捕获渲染器（使用BlackHole流）
            if (window.systemAudioCapture) {
              const success = await window.systemAudioCapture.startCaptureWithStream(stream, {
                sampleRate: 48000,
                channels: 2,
                bufferSize: 4096
              });

              if (success) {
                console.log('✅ BlackHole音频捕获启动成功');
                return true;
              } else {
                console.error('❌ BlackHole音频捕获启动失败');
                return false;
              }
            } else {
              console.error('❌ systemAudioCapture未初始化');
              return false;
            }
          } catch (error) {
            console.error('❌ BlackHole直接捕获失败:', error);
            return false;
          }
        })()
      `);

      return result;
    } catch (error) {
      console.error('BlackHole直接捕获过程失败:', error);
      return false;
    }
  }

  /**
   * 选择最佳音频源（优化Mac BlackHole支持）
   */
  private async selectBestAudioSource(sources: SystemAudioSource[], preferredSourceId?: string): Promise<string> {
    // 如果指定了首选源ID且存在，使用它
    if (preferredSourceId && sources.some(s => s.id === preferredSourceId)) {
      return preferredSourceId;
    }

    const isMac = process.platform === 'darwin';

    if (isMac) {
      // Mac平台特殊处理：检查是否有BlackHole相关的音频源
      console.log('Mac平台音频源选择，检查BlackHole设备...');

      // 首先尝试检测BlackHole相关的音频源
      // 注意：在Mac上，BlackHole通常不会直接出现在desktopCapturer.getSources()中
      // 但我们可以通过其他方式检测

      // 获取更详细的音频源信息
      try {
        const detailedSources = await desktopCapturer.getSources({
          types: ['screen', 'window'],
          fetchWindowIcons: true,
          thumbnailSize: { width: 150, height: 150 }
        });

        console.log('详细音频源列表:');
        detailedSources.forEach((source, index) => {
          console.log(`  ${index + 1}. ${source.name} (ID: ${source.id}, 类型: ${source.id.startsWith('screen') ? 'screen' : 'window'})`);
        });

        // 查找可能包含BlackHole音频的源
        const blackHoleSource = detailedSources.find(s =>
          s.name.toLowerCase().includes('blackhole') ||
          s.name.toLowerCase().includes('multi-output') ||
          s.name.toLowerCase().includes('aggregate')
        );

        if (blackHoleSource) {
          console.log('找到BlackHole相关音频源:', blackHoleSource.name);
          return blackHoleSource.id;
        }

        // 如果没找到BlackHole，选择主屏幕但给出警告
        const mainScreen = detailedSources.find(s =>
          s.name.toLowerCase().includes('entire') ||
          s.name.toLowerCase().includes('screen') ||
          s.id.startsWith('screen')
        );

        if (mainScreen) {
          console.log('Mac平台选择主屏幕音频源:', mainScreen.name);
          console.warn('警告：未检测到BlackHole设备，使用屏幕音频源可能无法捕获系统音频');
          console.warn('请确保：');
          console.warn('1. BlackHole已正确安装');
          console.warn('2. 已创建多输出设备并包含BlackHole');
          console.warn('3. 系统音频输出已设置为多输出设备');
          return mainScreen.id;
        }

      } catch (error) {
        console.error('获取详细音频源失败:', error);
      }
    } else {
      // Windows平台：优先选择"整个屏幕"或类似的源
      const mainScreen = sources.find(s =>
        s.name.includes('整个屏幕') ||
        s.name.toLowerCase().includes('entire') ||
        s.name.toLowerCase().includes('screen')
      );
      if (mainScreen) {
        console.log('Windows平台选择主屏幕音频源:', mainScreen.name);
        return mainScreen.id;
      }
    }

    // 如果有缓存的源ID且仍然有效，使用它
    if (this.selectedSourceId && sources.some(s => s.id === this.selectedSourceId)) {
      return this.selectedSourceId;
    }

    // 如果没找到合适的源，使用第一个可用源
    console.log('使用第一个可用音频源:', sources[0].name);
    return sources[0].id;
  }

  /**
   * 处理来自渲染进程的音频数据
   */
  handleAudioData(audioData: ArrayBuffer): void {
    if (this.isCapturing) {
      console.log(`主进程收到音频数据，大小: ${audioData.byteLength} 字节`);
      this.emit('audioData', audioData);
    } else {
      console.warn('主进程未在捕获状态，忽略音频数据');
    }
  }

  /**
   * 获取可用的音频源（带缓存优化，支持Mac平台）
   */
  private async getAvailableAudioSourcesCached(): Promise<SystemAudioSource[]> {
    const now = Date.now();
    const isMac = process.platform === 'darwin';

    // 检查缓存是否有效
    if (SystemAudioCapture.audioSourcesCache &&
        (now - SystemAudioCapture.cacheTimestamp) < SystemAudioCapture.CACHE_DURATION) {
      console.log('使用缓存的音频源列表');
      return SystemAudioCapture.audioSourcesCache;
    }

    try {
      console.log(`获取新的音频源列表 [${isMac ? 'Mac' : 'Other'}平台]...`);

      // Mac平台使用更详细的源获取配置
      const sourceOptions = isMac ? {
        types: ['screen' as const],
        fetchWindowIcons: true, // Mac上尝试获取更详细信息
        thumbnailSize: { width: 150, height: 150 } // 小缩略图以验证权限
      } : {
        types: ['screen' as const],
        fetchWindowIcons: false
      };

      const sources = await desktopCapturer.getSources(sourceOptions);

      let audioSources = sources.map(source => ({
        id: source.id,
        name: source.name,
        type: 'screen' as const
      }));

      // Mac平台特定处理：优先选择主屏幕
      if (isMac && audioSources.length > 1) {
        audioSources = audioSources.sort((a, b) => {
          // 将"Entire Screen"或包含"Screen"的源排在前面
          const aIsMain = a.name.toLowerCase().includes('entire') || a.name.toLowerCase().includes('screen');
          const bIsMain = b.name.toLowerCase().includes('entire') || b.name.toLowerCase().includes('screen');

          if (aIsMain && !bIsMain) return -1;
          if (!aIsMain && bIsMain) return 1;
          return 0;
        });

        console.log(`Mac平台音频源排序完成，优先源: ${audioSources[0]?.name}`);
      }

      // 更新缓存
      SystemAudioCapture.audioSourcesCache = audioSources;
      SystemAudioCapture.cacheTimestamp = now;

      console.log(`获取到 ${audioSources.length} 个音频源`);
      return audioSources;
    } catch (error) {
      console.error('获取音频源失败:', error);

      // Mac特定错误提示
      if (isMac) {
        console.error('Mac平台获取音频源失败，可能的原因：');
        console.error('1. 未授予屏幕录制权限');
        console.error('2. 系统安全设置阻止了访问');
        console.error('请检查"系统偏好设置 > 安全性与隐私 > 屏幕录制"');
      }

      // 如果有缓存，即使过期也返回缓存
      return SystemAudioCapture.audioSourcesCache || [];
    }
  }

  /**
   * 获取可用的音频源（兼容旧版本）
   */
  private async getAvailableAudioSources(): Promise<SystemAudioSource[]> {
    return this.getAvailableAudioSourcesCached();
  }

  /**
   * 确保渲染进程准备就绪
   */
  private async ensureRendererReady(): Promise<void> {
    if (!this.mainWindow) return;

    // 检查渲染进程是否已准备好
    const isReady = await this.mainWindow.webContents.executeJavaScript(`
      !!window.systemAudioCapture
    `);

    if (!isReady) {
      throw new Error('SystemAudioCaptureRenderer未初始化');
    }
  }

  /**
   * 在渲染进程中启动音频捕获（优化版本）
   */
  private async startCaptureInRenderer(): Promise<boolean> {
    if (!this.mainWindow || !this.selectedSourceId) return false;

    try {
      // 使用更高效的IPC通信方式
      const result = await this.mainWindow.webContents.executeJavaScript(`
        window.systemAudioCapture.startCapture('${this.selectedSourceId}', ${JSON.stringify(this.options)})
      `);

      console.log('渲染进程音频捕获结果:', result);
      return result;
    } catch (error) {
      console.error('渲染进程音频捕获失败:', error);
      return false;
    }
  }

  /**
   * 停止捕获系统音频（改进版本，确保完全清理）
   */
  async stopCapture(): Promise<void> {
    try {
      console.log('停止系统音频捕获...');

      this.isCapturing = false;

      // 通过IPC请求渲染进程停止音频捕获（异步等待）
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        try {
          await this.mainWindow.webContents.executeJavaScript(`
            (async () => {
              if (window.systemAudioCapture) {
                console.log('主进程请求停止渲染进程音频捕获');
                await window.systemAudioCapture.stopCapture();
                console.log('渲染进程音频捕获已停止');
                return true;
              }
              return false;
            })()
          `);

          // 等待一小段时间确保渲染进程完全清理
          await new Promise(resolve => setTimeout(resolve, 200));

        } catch (error) {
          console.warn('停止渲染进程音频捕获失败:', error);
        }
      }

      // 重置状态
      this.selectedSourceId = null;

      console.log('系统音频捕获已停止');
      this.emit('stopped');
    } catch (error) {
      console.error('停止系统音频捕获失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 检查系统音频捕获权限（支持Mac平台特定检查）
   */
  static async checkPermissions(): Promise<boolean> {
    try {
      const isMac = process.platform === 'darwin';

      // 使用缓存管理器检查权限
      const permissions = await audioCacheManager.getPermissions();

      if (isMac) {
        if (!permissions.systemAudio) {
          console.warn('Mac平台系统音频权限检查失败。请确保已授予屏幕录制权限：');
          console.warn('1. 打开"系统偏好设置"');
          console.warn('2. 选择"安全性与隐私"');
          console.warn('3. 点击"屏幕录制"标签');
          console.warn('4. 勾选本应用');
          console.warn('5. 重启应用');
        } else {
          // Mac平台额外检查：虚拟音频设备
          console.log('Mac平台权限检查通过，但请注意：');
          console.log('Mac系统不支持直接捕获系统音频输出。');
          console.log('如需捕获系统音频，请安装虚拟音频设备（如BlackHole）：');
          console.log('1. 下载安装 BlackHole: https://github.com/ExistentialAudio/BlackHole');
          console.log('2. 在"音频MIDI设置"中创建多输出设备');
          console.log('3. 将系统音频输出设置为多输出设备');
          console.log('4. 重启本应用');
        }
      }

      return permissions.systemAudio;
    } catch (error) {
      console.error('检查系统音频权限失败:', error);
      return false;
    }
  }

  /**
   * 获取可用的音频源列表（静态方法，使用缓存管理器）
   */
  static async getAudioSources(): Promise<SystemAudioSource[]> {
    try {
      return await audioCacheManager.getAudioSources();
    } catch (error) {
      console.error('获取音频源失败:', error);
      return [];
    }
  }

  /**
   * 清除音频源缓存和预初始化状态
   */
  static clearCache(): void {
    SystemAudioCapture.audioSourcesCache = null;
    SystemAudioCapture.cacheTimestamp = 0;
    SystemAudioCapture.globalPreInitialized = false;
    SystemAudioCapture.preInitPromise = null;
    audioCacheManager.clearCache(); // 同时清除缓存管理器的缓存
    console.log('系统音频缓存和预初始化状态已清除');
  }

  /**
   * 检查当前是否正在捕获
   */
  isActive(): boolean {
    return this.isCapturing;
  }

  /**
   * 获取当前配置
   */
  getOptions(): Required<SystemAudioCaptureOptions> {
    return { ...this.options };
  }
}

/**
 * 音频缓存管理器
 * 负责管理音频源、权限状态和预初始化状态的缓存
 */

import { desktopCapturer } from 'electron';

export interface AudioCacheData {
  audioSources: Array<{
    id: string;
    name: string;
    type: 'screen' | 'window';
  }>;
  permissions: {
    systemAudio: boolean;
    microphone: boolean;
  };
  lastUpdated: number;
}

export class AudioCacheManager {
  private static instance: AudioCacheManager | null = null;
  private cache: AudioCacheData | null = null;
  private readonly CACHE_DURATION = 2 * 60 * 60 * 1000; // 2小时缓存
  private isInitializing = false;
  private initPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): AudioCacheManager {
    if (!AudioCacheManager.instance) {
      AudioCacheManager.instance = new AudioCacheManager();
    }
    return AudioCacheManager.instance;
  }

  /**
   * 预初始化音频缓存
   */
  async preInitialize(): Promise<void> {
    if (this.isInitializing) {
      return this.initPromise || Promise.resolve();
    }

    if (this.isCacheValid()) {
      console.log('音频缓存已有效，跳过预初始化');
      return;
    }

    this.isInitializing = true;
    this.initPromise = this.doPreInitialize();
    
    try {
      await this.initPromise;
    } finally {
      this.isInitializing = false;
      this.initPromise = null;
    }
  }

  /**
   * 执行实际的预初始化工作
   */
  private async doPreInitialize(): Promise<void> {
    try {
      console.log('开始音频缓存预初始化...');

      // 并行获取音频源和权限信息
      const [audioSources, permissions] = await Promise.allSettled([
        this.fetchAudioSources(),
        this.checkPermissions()
      ]);

      this.cache = {
        audioSources: audioSources.status === 'fulfilled' ? audioSources.value : [],
        permissions: permissions.status === 'fulfilled' ? permissions.value : {
          systemAudio: false,
          microphone: false
        },
        lastUpdated: Date.now()
      };

      console.log('音频缓存预初始化完成，音频源数量:', this.cache.audioSources.length);
    } catch (error) {
      console.warn('音频缓存预初始化失败:', error);
    }
  }

  /**
   * 获取音频源（带缓存）
   */
  async getAudioSources(): Promise<Array<{ id: string; name: string; type: 'screen' | 'window' }>> {
    if (!this.isCacheValid()) {
      await this.preInitialize();
    }

    return this.cache?.audioSources || [];
  }

  /**
   * 获取权限状态（带缓存）
   */
  async getPermissions(): Promise<{ systemAudio: boolean; microphone: boolean }> {
    if (!this.isCacheValid()) {
      await this.preInitialize();
    }

    return this.cache?.permissions || { systemAudio: false, microphone: false };
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    if (!this.cache) return false;
    
    const now = Date.now();
    return (now - this.cache.lastUpdated) < this.CACHE_DURATION;
  }

  /**
   * 获取音频源列表（Mac BlackHole优化版本）
   */
  private async fetchAudioSources(): Promise<Array<{ id: string; name: string; type: 'screen' | 'window' }>> {
    try {
      console.log('开始获取音频源列表...');
      const isMac = process.platform === 'darwin';

      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        fetchWindowIcons: false
      });

      const audioSources = sources.map(source => ({
        id: source.id,
        name: source.name,
        type: 'screen' as const
      }));

      console.log('获取到的音频源列表:');
      audioSources.forEach((source, index) => {
        console.log(`  ${index + 1}. ${source.name} (ID: ${source.id})`);
      });

      // Mac平台特殊提示
      if (isMac) {
        console.log('');
        console.log('📝 Mac平台注意事项:');
        console.log('- 上述音频源为屏幕捕获源，可能无法捕获系统音频');
        console.log('- 建议使用BlackHole虚拟音频设备进行系统音频捕获');
        console.log('- BlackHole设备需要通过navigator.mediaDevices.enumerateDevices()检测');
        console.log('');
      }

      if (audioSources.length === 0) {
        console.warn('未找到任何音频源，可能的原因：');
        console.warn('1. 系统权限不足');
        console.warn('2. 没有可用的屏幕或窗口');
        console.warn('3. 系统安全设置阻止了访问');

        if (isMac) {
          console.warn('4. Mac平台建议安装BlackHole虚拟音频设备');
        }
      }

      return audioSources;
    } catch (error) {
      console.error('获取音频源失败:', error);
      return [];
    }
  }

  /**
   * 检查权限状态（支持Mac平台特定检查）
   */
  private async checkPermissions(): Promise<{ systemAudio: boolean; microphone: boolean }> {
    try {
      const isMac = process.platform === 'darwin';

      // 检查系统音频权限
      const systemAudioSources = await desktopCapturer.getSources({
        types: ['screen'],
        fetchWindowIcons: false
      });

      let systemAudioPermission = systemAudioSources.length > 0;

      // Mac平台需要额外的权限验证
      if (isMac && systemAudioPermission) {
        try {
          // 尝试实际测试音频捕获能力
          systemAudioPermission = await this.testMacAudioCapture(systemAudioSources[0]?.id);
        } catch (error) {
          console.warn('Mac音频捕获测试失败:', error);
          systemAudioPermission = false;
        }
      }

      return {
        systemAudio: systemAudioPermission,
        microphone: true // 麦克风权限通常在运行时检查
      };
    } catch (error) {
      console.error('检查权限失败:', error);
      return {
        systemAudio: false,
        microphone: false
      };
    }
  }

  /**
   * Mac平台音频捕获能力测试
   */
  private async testMacAudioCapture(sourceId: string): Promise<boolean> {
    if (!sourceId) return false;

    try {
      // 这里我们不能直接在主进程中测试getUserMedia
      // 但可以通过其他方式验证权限

      // 检查是否有屏幕录制权限的间接方法
      // 在Mac上，如果没有屏幕录制权限，getSources通常会返回空数组或受限的结果
      const detailedSources = await desktopCapturer.getSources({
        types: ['screen', 'window'],
        fetchWindowIcons: true // 需要更高权限
      });

      // 如果能获取到详细的源信息（包括图标），说明权限较为完整
      const hasDetailedAccess = detailedSources.some(source =>
        source.thumbnail && source.thumbnail.getSize().width > 0
      );

      console.log(`Mac权限检查: 详细访问=${hasDetailedAccess}, 源数量=${detailedSources.length}`);
      return hasDetailedAccess;
    } catch (error) {
      console.warn('Mac音频捕获测试异常:', error);
      return false;
    }
  }

  /**
   * 强制刷新缓存
   */
  async refreshCache(): Promise<void> {
    this.cache = null;
    await this.preInitialize();
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = null;
    console.log('音频缓存已清除');
  }

  /**
   * 获取缓存状态信息
   */
  getCacheInfo(): { isValid: boolean; lastUpdated: number | null; sourceCount: number } {
    return {
      isValid: this.isCacheValid(),
      lastUpdated: this.cache?.lastUpdated || null,
      sourceCount: this.cache?.audioSources.length || 0
    };
  }
}

// 导出单例实例
export const audioCacheManager = AudioCacheManager.getInstance();

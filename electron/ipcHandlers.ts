// ipcHandlers.ts

import { ipc<PERSON>ain, shell, dialog, <PERSON><PERSON>er<PERSON>indow } from "electron"
import { randomBytes } from "crypto"
import { IIpcHandlerDeps } from "./main"
import { configHelper } from "./ConfigHelper"
import { voiceConfigHelper } from "./VoiceConfigHelper"
import { VocabularyApiHelper } from "./VocabularyApiHelper"
import { customPromptsHelper } from "./CustomPromptsHelper"
import { logHelper } from "./LogHelper"
import { cheatSheetHelper } from "./CheatSheetHelper"
import { state } from "./main"  // 导入state对象
import { GummyWebSocketHandler } from "./GummyWebSocketHandler"
import { SystemAudioCapture } from "./SystemAudioCapture"
import * as axios from "axios"

export function initializeIpcHandlers(deps: IIpcHandlerDeps): void {
  console.log("Initializing IPC handlers")

  // Configuration handlers
  ipcMain.handle("get-config", () => {
    return configHelper.loadConfig();
  })

  ipcMain.handle("update-config", (_event, updates) => {
    return configHelper.updateConfig(updates);
  })

  ipcMain.handle("check-api-key", () => {
    return configHelper.hasApiKey();
  })
  
  ipcMain.handle("validate-api-key", async (_event, apiKey) => {
    // First check the format
    if (!configHelper.isValidApiKeyFormat(apiKey)) {
      return {
        valid: false,
        error: "Invalid API key format. OpenRouter API keys start with 'sk-or-'"
      };
    }

    // Then test the API key with OpenRouter
    const result = await configHelper.testApiKey(apiKey);
    return result;
  })

  // 模型验证处理器
  ipcMain.handle("validate-model", async (_event, modelId, apiKey, requiresImages = false) => {
    try {
      // 只对冒号进行编码，保留斜杠
      const encodedModelId = modelId.replace(/:/g, '%3A');
      const response = await axios.default.get(`https://openrouter.ai/api/v1/models/${encodedModelId}/endpoints`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://github.com'
        }
      });

      const responseData = response.data;

      // 根据官方API文档，数据在data字段中
      if (!responseData.data) {
        return {
          isValid: false,
          supportsImages: false,
          error: 'API响应格式错误'
        };
      }

      const modelData = responseData.data;

      // 检查endpoints是否为空
      if (!modelData.endpoints || !Array.isArray(modelData.endpoints) || modelData.endpoints.length === 0) {
        return {
          isValid: false,
          supportsImages: false,
          error: '模型暂无可用端点'
        };
      }

      // 检查是否支持图像输入 - 从模型的architecture中获取
      const supportsImages = modelData.architecture?.input_modalities?.includes('image') || false;

      // 如果需要图像支持但模型不支持
      if (requiresImages && !supportsImages) {
        return {
          isValid: false,
          supportsImages: false,
          error: '该模型不支持图像分析，无法用于问题提取或调试功能'
        };
      }

      return {
        isValid: true,
        supportsImages,
        endpoints: modelData.endpoints,
        modelInfo: {
          id: modelData.id,
          name: modelData.name,
          description: modelData.description,
          architecture: modelData.architecture
        }
      };
    } catch (error: any) {
      console.error('模型验证失败:', error);

      // 处理axios错误
      if (error.response) {
        if (error.response.status === 404) {
          return {
            isValid: false,
            supportsImages: false,
            error: '模型不存在或无法访问'
          };
        }
        return {
          isValid: false,
          supportsImages: false,
          error: `API请求失败: ${error.response.status} ${error.response.statusText}`
        };
      }

      return {
        isValid: false,
        supportsImages: false,
        error: error.message || '网络请求失败'
      };
    }
  })

  // Credits handlers
  ipcMain.handle("set-initial-credits", async (_event, credits: number) => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) return

    try {
      // Set the credits in a way that ensures atomicity
      await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ = ${credits}`
      )
      mainWindow.webContents.send("credits-updated", credits)
    } catch (error) {
      console.error("Error setting initial credits:", error)
      throw error
    }
  })

  ipcMain.handle("decrement-credits", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) return

    try {
      const currentCredits = await mainWindow.webContents.executeJavaScript(
        "window.__CREDITS__"
      )
      if (currentCredits > 0) {
        const newCredits = currentCredits - 1
        await mainWindow.webContents.executeJavaScript(
          `window.__CREDITS__ = ${newCredits}`
        )
        mainWindow.webContents.send("credits-updated", newCredits)
      }
    } catch (error) {
      console.error("Error decrementing credits:", error)
    }
  })

  // Screenshot queue handlers
  ipcMain.handle("get-screenshot-queue", () => {
    return deps.getScreenshotQueue()
  })

  ipcMain.handle("get-extra-screenshot-queue", () => {
    return deps.getExtraScreenshotQueue()
  })

  ipcMain.handle("delete-screenshot", async (event, path: string) => {
    return deps.deleteScreenshot(path)
  })

  ipcMain.handle("get-image-preview", async (event, path: string) => {
    return deps.getImagePreview(path)
  })

  // Screenshot processing handlers
  ipcMain.handle("process-screenshots", async () => {
    // Check for API key before processing
    if (!configHelper.hasApiKey()) {
      const mainWindow = deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send(deps.PROCESSING_EVENTS.API_KEY_INVALID);
      }
      return;
    }
    
    await deps.processingHelper?.processScreenshots()
  })

  // Window dimension handlers
  ipcMain.handle(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        deps.setWindowDimensions(width, height)
      }
    }
  )

  ipcMain.handle(
    "set-window-dimensions",
    (event, width: number, height: number) => {
      deps.setWindowDimensions(width, height)
    }
  )

  // Screenshot management handlers
  ipcMain.handle("get-screenshots", async () => {
    try {
      let previews = []
      const currentView = deps.getView()

      if (currentView === "queue") {
        const queue = deps.getScreenshotQueue()
        previews = await Promise.all(
          queue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      } else {
        const extraQueue = deps.getExtraScreenshotQueue()
        previews = await Promise.all(
          extraQueue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      }

      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  })

  // Screenshot trigger handlers
  ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      try {
        const screenshotPath = await deps.takeScreenshot()
        const preview = await deps.getImagePreview(screenshotPath)
        mainWindow.webContents.send("screenshot-taken", {
          path: screenshotPath,
          preview
        })
        return { success: true }
      } catch (error) {
        console.error("Error triggering screenshot:", error)
        return { error: "Failed to trigger screenshot" }
      }
    }
    return { error: "No main window available" }
  })

  ipcMain.handle("take-screenshot", async () => {
    try {
      const screenshotPath = await deps.takeScreenshot()
      const preview = await deps.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      return { error: "Failed to take screenshot" }
    }
  })

  // Auth-related handlers removed

  ipcMain.handle("open-external-url", (event, url: string) => {
    shell.openExternal(url)
  })
  
  // Open external URL handler
  ipcMain.handle("openLink", (event, url: string) => {
    try {
      console.log(`Opening external URL: ${url}`);
      shell.openExternal(url);
      return { success: true };
    } catch (error) {
      console.error(`Error opening URL ${url}:`, error);
      return { success: false, error: `Failed to open URL: ${error}` };
    }
  })

  // Settings portal handler
  ipcMain.handle("open-settings-portal", () => {
    const mainWindow = deps.getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("show-settings-dialog");
      return { success: true };
    }
    return { success: false, error: "Main window not available" };
  })

  // Window management handlers
  ipcMain.handle("toggle-window", () => {
    try {
      deps.toggleMainWindow()
      return { success: true }
    } catch (error) {
      console.error("Error toggling window:", error)
      return { error: "Failed to toggle window" }
    }
  })

  // 新增：鼠标穿透控制处理器
  ipcMain.handle("toggle-mouse-click-through", () => {
    try {
      deps.toggleMouseClickThrough()
      return { success: true }
    } catch (error) {
      console.error("Error toggling mouse click through:", error)
      return { error: "Failed to toggle mouse click through" }
    }
  })

  ipcMain.handle("reset-queues", async () => {
    try {
      deps.clearQueues()
      return { success: true }
    } catch (error) {
      console.error("Error resetting queues:", error)
      return { error: "Failed to reset queues" }
    }
  })

  // Process screenshot handlers
  ipcMain.handle("trigger-process-screenshots", async () => {
    try {
      // Check for API key before processing
      if (!configHelper.hasApiKey()) {
        const mainWindow = deps.getMainWindow();
        if (mainWindow) {
          mainWindow.webContents.send(deps.PROCESSING_EVENTS.API_KEY_INVALID);
        }
        return { success: false, error: "API key required" };
      }

      await deps.processingHelper?.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { error: "Failed to process screenshots" }
    }
  })

  // 直接解答模式处理器
  ipcMain.handle("trigger-direct-answer", async () => {
    try {
      // Check for API key before processing
      if (!configHelper.hasApiKey()) {
        const mainWindow = deps.getMainWindow();
        if (mainWindow) {
          mainWindow.webContents.send(deps.PROCESSING_EVENTS.API_KEY_INVALID);
        }
        return { success: false, error: "API key required" };
      }

      await deps.processingHelper?.processDirectAnswer()
      return { success: true }
    } catch (error) {
      console.error("Error processing direct answer:", error)
      return { error: "Failed to process direct answer" }
    }
  })

  // 直接调试模式处理器
  ipcMain.handle("trigger-direct-debug", async () => {
    try {
      // Check for API key before processing
      if (!configHelper.hasApiKey()) {
        const mainWindow = deps.getMainWindow();
        if (mainWindow) {
          mainWindow.webContents.send(deps.PROCESSING_EVENTS.API_KEY_INVALID);
        }
        return { success: false, error: "API key required" };
      }

      await deps.processingHelper?.processDirectDebug()
      return { success: true }
    } catch (error) {
      console.error("Error processing direct debug:", error)
      return { error: "Failed to process direct debug" }
    }
  })

  // Reset handlers
  ipcMain.handle("trigger-reset", () => {
    try {
      // First cancel any ongoing requests
      deps.processingHelper?.cancelOngoingRequests()

      // Clear all queues immediately
      deps.clearQueues()

      // Reset view to queue
      deps.setView("queue")

      // Get main window and send reset events
      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        // Send reset events in sequence
        mainWindow.webContents.send("reset-view")
        mainWindow.webContents.send("reset")
      }

      return { success: true }
    } catch (error) {
      console.error("Error triggering reset:", error)
      return { error: "Failed to trigger reset" }
    }
  })

  // Window movement handlers
  ipcMain.handle("trigger-move-left", () => {
    try {
      deps.moveWindowLeft()
      return { success: true }
    } catch (error) {
      console.error("Error moving window left:", error)
      return { error: "Failed to move window left" }
    }
  })

  ipcMain.handle("trigger-move-right", () => {
    try {
      deps.moveWindowRight()
      return { success: true }
    } catch (error) {
      console.error("Error moving window right:", error)
      return { error: "Failed to move window right" }
    }
  })

  ipcMain.handle("trigger-move-up", () => {
    try {
      deps.moveWindowUp()
      return { success: true }
    } catch (error) {
      console.error("Error moving window up:", error)
      return { error: "Failed to move window up" }
    }
  })

  ipcMain.handle("trigger-move-down", () => {
    try {
      deps.moveWindowDown()
      return { success: true }
    } catch (error) {
      console.error("Error moving window down:", error)
      return { error: "Failed to move window down" }
    }
  })
  
  // Delete last screenshot handler
  ipcMain.handle("delete-last-screenshot", async () => {
    try {
      const queue = deps.getView() === "queue" 
        ? deps.getScreenshotQueue() 
        : deps.getExtraScreenshotQueue()
      
      if (queue.length === 0) {
        return { success: false, error: "No screenshots to delete" }
      }
      
      // Get the last screenshot in the queue
      const lastScreenshot = queue[queue.length - 1]
      
      // Delete it
      const result = await deps.deleteScreenshot(lastScreenshot)
      
      // Notify the renderer about the change
      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("screenshot-deleted", { path: lastScreenshot })
      }
      
      return result
    } catch (error) {
      console.error("Error deleting last screenshot:", error)
      return { success: false, error: "Failed to delete last screenshot" }
    }
  })

  // 添加流式调用处理程序
  ipcMain.handle('stream-chat', async (_, { messages, eventSourceId }) => {
    if (!deps.processingHelper) {
      throw new Error("处理助手未初始化");
    }
    
    // 创建用于取消请求的控制器
    const abortController = new AbortController();
    const { signal } = abortController;
    
    try {
      // 检查激活状态
      if (!deps.isActivated) {
        console.log("stream-chat: 应用未激活，检查最新状态");
        // 再次检查激活状态（可能在此期间已激活）
        if (deps.activationHelper) {
          deps.isActivated = await deps.activationHelper.isActivated();
        }
        
        if (!deps.isActivated) {
          console.log("stream-chat: 确认应用未激活，返回错误");
          const mainWindow = deps.getMainWindow();
          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, "应用未激活，请先激活应用");
            mainWindow.webContents.send('activation-required');
            mainWindow.webContents.send(deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED);
          }
          throw new Error("应用未激活");
        }
      }
      
      // 消耗调用次数
      if (deps.activationHelper) {
        const result = await deps.activationHelper.consumeCall();
        if (!result.success) {
          const mainWindow = deps.getMainWindow();
          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, result.message);
            
            // 如果是因为调用次数用尽，发送特定事件
            if (result.message.includes('用尽') || result.message.includes('expired')) {
              mainWindow.webContents.send(deps.PROCESSING_EVENTS.OUT_OF_CREDITS);
            }
          }
          throw new Error(result.message);
        }
        
        // 更新界面显示剩余调用次数
        const mainWindow = deps.getMainWindow();
        if (result.remainingCalls !== undefined && mainWindow) {
          mainWindow.webContents.send(
            "remaining-calls-updated", 
            { remainingCalls: result.remainingCalls }
          );
        }
      }
      
      // 调用流式处理方法
      await deps.processingHelper.streamChat(
        messages, 
        signal, 
        eventSourceId,
        deps.getMainWindow()
      );
    } catch (error) {
      console.error("流式对话处理出错:", error);
      const mainWindow = deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send(`${eventSourceId}-error`, error instanceof Error ? error.message : String(error));
      }
      throw error;
    }
  });

  // 添加语音模式切换处理程序
  ipcMain.handle('toggle-voice-chat-mode', () => {
    // 直接处理语音模式切换
    const mainWindow = deps.getMainWindow();
    // 更新语音模式状态
    deps.isVoiceChatMode = !deps.isVoiceChatMode;
    console.log(`语音聊天模式: ${deps.isVoiceChatMode ? '开启' : '关闭'}`);
    
    // 确保快捷键助手能获取到最新的语音模式状态
    if (state) {
      state.isVoiceChatMode = deps.isVoiceChatMode;
    }
    
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('voice-chat-mode-changed', deps.isVoiceChatMode);
    }
    
    return deps.isVoiceChatMode;
  });

  // 添加获取日志路径的处理程序
  ipcMain.handle("get-log-path", () => {
    try {
      // 从LogHelper获取日志路径
      const logPath = logHelper.getElectronLogPath();
      return logPath;
    } catch (error) {
      console.error("获取日志路径出错:", error);
      return null;
    }
  });

  // Voice configuration handlers
  ipcMain.handle("get-voice-config", () => {
    try {
      return voiceConfigHelper.loadConfig();
    } catch (error) {
      console.error("Error loading voice config:", error);
      throw error;
    }
  });

  ipcMain.handle("update-voice-config", (event, config) => {
    try {
      const updatedConfig = voiceConfigHelper.updateConfig(config);

      // 向所有窗口发送语音配置更新事件
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        window.webContents.send('voice-config-updated', updatedConfig);
      });

      return updatedConfig;
    } catch (error) {
      console.error("Error updating voice config:", error);
      throw error;
    }
  });

  ipcMain.handle("test-voice-credentials", async (event, provider, credentials) => {
    try {
      const result = await voiceConfigHelper.testCredentials(provider, credentials);
      return result;
    } catch (error) {
      console.error("Error testing voice credentials:", error);
      return { valid: false, error: "测试凭据时出错" };
    }
  });

  ipcMain.handle("get-voice-provider-config", (event, provider) => {
    try {
      return voiceConfigHelper.getProviderConfig(provider);
    } catch (error) {
      console.error("Error getting provider config:", error);
      throw error;
    }
  });

  ipcMain.handle("is-voice-provider-configured", (event, provider) => {
    try {
      return voiceConfigHelper.isProviderConfigured(provider);
    } catch (error) {
      console.error("Error checking provider configuration:", error);
      return false;
    }
  });

  // 语音识别WebSocket处理器 - 支持双音频源
  let gummyHandlers: Map<string, GummyWebSocketHandler> = new Map(); // 使用Map管理多个处理器

  // 获取或创建指定音频源的处理器
  const getOrCreateGummyHandler = async (sourceIdentifier: string, config?: any, language?: string): Promise<GummyWebSocketHandler> => {
    let handler = gummyHandlers.get(sourceIdentifier);

    if (!handler || !handler.isConnectedToService()) {
      if (handler) {
        handler.disconnect();
      }

      // 获取激活的热词表ID
      const activeVocabularyTable = voiceConfigHelper.getActiveVocabularyTable();
      const vocabularyId = activeVocabularyTable?.vocabularyId;

      handler = new GummyWebSocketHandler({
        config: config || {},
        language: language || 'zh',
        vocabularyId,
        sourceIdentifier,
        onResult: (transcription) => {
          const mainWindow = deps.getMainWindow();
          if (mainWindow) {
            mainWindow.webContents.send('gummy-result', transcription);
          }
        },
        onError: (error) => {
          const mainWindow = deps.getMainWindow();
          if (mainWindow) {
            console.log(`发送 gummy-error 事件，sourceIdentifier: ${sourceIdentifier}, error: ${error}`);
            mainWindow.webContents.send('gummy-error', { error, sourceIdentifier });
          }
        },
        onEnd: () => {
          const mainWindow = deps.getMainWindow();
          if (mainWindow) {
            console.log(`发送 gummy-end 事件，sourceIdentifier: ${sourceIdentifier}`);
            mainWindow.webContents.send('gummy-end', { sourceIdentifier });
          }
        }
      });

      // 监听任务开始事件
      handler.on('task-started', () => {
        const mainWindow = deps.getMainWindow();
        if (mainWindow) {
          mainWindow.webContents.send('gummy-task-started', { sourceIdentifier });
        }
      });

      // 监听任务完成事件
      handler.on('task-finished', () => {
        const mainWindow = deps.getMainWindow();
        if (mainWindow) {
          mainWindow.webContents.send('gummy-task-finished', { sourceIdentifier });
        }
      });

      await handler.connect();
      gummyHandlers.set(sourceIdentifier, handler);
      console.log(`Gummy WebSocket连接已建立 [${sourceIdentifier}]`);
    }

    return handler;
  };

  // 系统音频捕获处理器
  let systemAudioCapture: SystemAudioCapture | null = null;

  // 获取或创建Gummy处理器（新API，支持双音频源）
  ipcMain.handle("get-or-create-gummy-handler", async (event, sourceIdentifier, config, language = 'zh') => {
    try {
      await getOrCreateGummyHandler(sourceIdentifier, config, language);
      return { success: true };
    } catch (error) {
      console.error(`Error creating Gummy handler [${sourceIdentifier}]:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 检查Gummy连接状态并支持重连
  ipcMain.handle("gummy-check-and-reconnect", async (event, sourceIdentifier = 'microphone') => {
    try {
      const handler = gummyHandlers.get(sourceIdentifier);

      if (!handler) {
        return { success: false, connected: false, error: `Gummy处理器未初始化 [${sourceIdentifier}]` };
      }

      const isConnected = handler.isConnectedToService();

      if (!isConnected) {
        console.log(`Gummy连接已断开，尝试重连 [${sourceIdentifier}]...`);
        const reconnectSuccess = await handler.reconnect();
        return {
          success: reconnectSuccess,
          connected: reconnectSuccess,
          error: reconnectSuccess ? null : `重连失败 [${sourceIdentifier}]`
        };
      }

      return { success: true, connected: true };
    } catch (error) {
      console.error(`Error checking/reconnecting Gummy [${sourceIdentifier}]:`, error);
      return {
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  });

  // 建立Gummy连接（进入语音聊天模式时调用）
  ipcMain.handle("gummy-connect", async (event, config, language = 'zh') => {
    try {
      // 为了向后兼容，默认创建麦克风处理器
      await getOrCreateGummyHandler('microphone', config, language);
      return { success: true };
    } catch (error) {
      console.error("Error connecting to Gummy:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 开始Gummy任务（开始语音录制时调用）
  ipcMain.handle("gummy-start-task", async (event, sourceIdentifier = 'microphone') => {
    try {
      let handler = gummyHandlers.get(sourceIdentifier);

      // 如果处理器不存在或连接已断开，尝试重连
      if (!handler || !handler.isConnectedToService()) {
        console.log(`Gummy处理器未连接，尝试重连 [${sourceIdentifier}]...`);

        if (handler) {
          // 如果处理器存在但连接断开，尝试重连
          const reconnectSuccess = await handler.reconnect();
          if (!reconnectSuccess) {
            return { success: false, error: `Gummy重连失败 [${sourceIdentifier}]` };
          }
        } else {
          // 如果处理器不存在，返回错误提示需要先建立连接
          return { success: false, error: `Gummy处理器未初始化，请先建立连接 [${sourceIdentifier}]` };
        }
      }

      handler.startTask();
      console.log(`Gummy任务已启动 [${sourceIdentifier}]`);
      return { success: true };
    } catch (error) {
      console.error(`Error starting Gummy task [${sourceIdentifier}]:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 结束Gummy任务（结束语音录制时调用）
  ipcMain.handle("gummy-finish-task", async (event, sourceIdentifier = 'microphone') => {
    try {
      const handler = gummyHandlers.get(sourceIdentifier);

      if (!handler || !handler.isConnectedToService()) {
        // 如果连接已断开，任务也就自然结束了，返回成功
        console.log(`Gummy连接已断开，任务自然结束 [${sourceIdentifier}]`);
        return { success: true };
      }

      handler.finishTask();
      console.log(`Gummy任务已结束 [${sourceIdentifier}]`);
      return { success: true };
    } catch (error) {
      console.error(`Error finishing Gummy task [${sourceIdentifier}]:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 断开Gummy连接（退出语音聊天模式时调用）
  ipcMain.handle("gummy-disconnect", async (event) => {
    try {
      console.log('断开所有通义Gummy WebSocket连接...');

      gummyHandlers.forEach((handler, sourceIdentifier) => {
        if (handler.isConnectedToService()) {
          console.log(`断开通义Gummy WebSocket连接 [${sourceIdentifier}]...`);
          handler.disconnect();
        }
      });

      gummyHandlers.clear();
      console.log('所有通义Gummy WebSocket连接已断开');
      return { success: true };
    } catch (error) {
      console.error("Error disconnecting Gummy:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 兼容旧的API（保持向后兼容）
  ipcMain.handle("gummy-start-recognition", async (event, config, language = 'zh') => {
    try {
      // 使用新的API来处理
      await getOrCreateGummyHandler('microphone', config, language);

      const handler = gummyHandlers.get('microphone');
      if (handler) {
        handler.startTask();
      }

      return { success: true };
    } catch (error) {
      console.error("Error starting Gummy recognition:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  ipcMain.handle("gummy-send-audio", (event, audioData, sourceIdentifier = 'microphone') => {
    try {
      const handler = gummyHandlers.get(sourceIdentifier);

      if (handler && handler.isConnectedToService() && handler.isTaskActive()) {
        handler.sendAudioData(Buffer.from(audioData));
        return { success: true };
      }
      return { success: false, error: `Gummy未连接或任务未激活 [${sourceIdentifier}]` };
    } catch (error) {
      console.error(`Error sending audio to Gummy [${sourceIdentifier}]:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 兼容旧的停止API
  ipcMain.handle("gummy-stop-recognition", () => {
    try {
      const handler = gummyHandlers.get('microphone');
      if (handler) {
        handler.finishTask();
        // 注意：这里不断开连接，保持连接复用
      }
      return { success: true };
    } catch (error) {
      console.error("Error stopping Gummy recognition:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 自定义提示词配置处理器
  ipcMain.handle("get-custom-prompts-config", () => {
    try {
      return customPromptsHelper.loadConfig();
    } catch (error) {
      console.error("Failed to get custom prompts config:", error);
      return null;
    }
  });

  ipcMain.handle("update-custom-prompts-config", (_event, config) => {
    try {
      customPromptsHelper.saveConfig(config);
      return true;
    } catch (error) {
      console.error("Failed to update custom prompts config:", error);
      return false;
    }
  });

  ipcMain.handle("get-default-custom-prompts-config", (_event, language?: 'chinese' | 'english') => {
    try {
      if (language === 'english') {
        return customPromptsHelper.getDefaultEnglishConfig();
      } else {
        return customPromptsHelper.getDefaultChineseConfig();
      }
    } catch (error) {
      console.error("Failed to get default custom prompts config:", error);
      return null;
    }
  });

  // 小抄相关处理器
  ipcMain.handle("cheatsheet-get-documents", async () => {
    try {
      return cheatSheetHelper.getDocuments();
    } catch (error) {
      console.error("获取小抄文档失败:", error);
      return [];
    }
  });

  ipcMain.handle("cheatsheet-get-current-document", async () => {
    try {
      return cheatSheetHelper.getCurrentDocument();
    } catch (error) {
      console.error("获取当前小抄文档失败:", error);
      return null;
    }
  });

  ipcMain.handle("cheatsheet-create-markdown", async (event, name, content) => {
    try {
      return await cheatSheetHelper.createMarkdownDocument(name, content);
    } catch (error) {
      console.error("创建Markdown文档失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-select-file", async () => {
    try {
      return await cheatSheetHelper.selectFile();
    } catch (error) {
      console.error("选择文件失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-add-local-file", async (event, filePath) => {
    try {
      return await cheatSheetHelper.addLocalFileDocument(filePath);
    } catch (error) {
      console.error("添加本地文件失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-get-document-content", async (event, document) => {
    try {
      return await cheatSheetHelper.getDocumentContent(document);
    } catch (error) {
      console.error("获取文档内容失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-search", async (event, document, searchTerm) => {
    try {
      return await cheatSheetHelper.searchInDocument(document, searchTerm);
    } catch (error) {
      console.error("搜索文档失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-next-document", async () => {
    try {
      return cheatSheetHelper.nextDocument();
    } catch (error) {
      console.error("切换到下一个文档失败:", error);
      return null;
    }
  });

  ipcMain.handle("cheatsheet-previous-document", async () => {
    try {
      return cheatSheetHelper.previousDocument();
    } catch (error) {
      console.error("切换到上一个文档失败:", error);
      return null;
    }
  });

  ipcMain.handle("cheatsheet-set-current-document", async (event, index) => {
    try {
      cheatSheetHelper.setCurrentDocument(index);
      return { success: true };
    } catch (error) {
      console.error("设置当前文档失败:", error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle("cheatsheet-delete-document", async (event, id) => {
    try {
      return cheatSheetHelper.deleteDocument(id);
    } catch (error) {
      console.error("删除文档失败:", error);
      return false;
    }
  });

  ipcMain.handle("cheatsheet-update-document", async (event, id, updates) => {
    try {
      return cheatSheetHelper.updateDocument(id, updates);
    } catch (error) {
      console.error("更新文档失败:", error);
      return false;
    }
  });

  ipcMain.handle("cheatsheet-select-directory", async () => {
    try {
      return await cheatSheetHelper.selectDirectory();
    } catch (error) {
      console.error("选择目录失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-scan-directory", async (event, directoryPath) => {
    try {
      return await cheatSheetHelper.scanDirectory(directoryPath);
    } catch (error) {
      console.error("扫描目录失败:", error);
      throw error;
    }
  });

  ipcMain.handle("cheatsheet-add-local-directory", async (event, directory) => {
    try {
      cheatSheetHelper.addLocalDirectory(directory);
      return { success: true };
    } catch (error) {
      console.error("添加本地目录失败:", error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle("cheatsheet-remove-local-directory", async (event, directory) => {
    try {
      cheatSheetHelper.removeLocalDirectory(directory);
      return { success: true };
    } catch (error) {
      console.error("删除本地目录失败:", error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle("cheatsheet-get-local-directories", async () => {
    try {
      return cheatSheetHelper.getLocalDirectories();
    } catch (error) {
      console.error("获取本地目录失败:", error);
      return [];
    }
  });

  // 系统操作处理器
  ipcMain.handle("open-external", async (event, path) => {
    try {
      await shell.openPath(path);
      return { success: true };
    } catch (error) {
      console.error("打开外部文件失败:", error);
      return { success: false, error: error.message };
    }
  });

  // 图片文件处理器
  ipcMain.handle("get-image-data-url", async (event, imagePath) => {
    try {
      const fs = require('fs');
      const path = require('path');

      if (!fs.existsSync(imagePath)) {
        throw new Error('图片文件不存在');
      }

      // 读取图片文件
      const imageBuffer = fs.readFileSync(imagePath);
      const ext = path.extname(imagePath).toLowerCase();

      // 确定MIME类型
      let mimeType = 'image/png'; // 默认
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          mimeType = 'image/jpeg';
          break;
        case '.png':
          mimeType = 'image/png';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        case '.svg':
          mimeType = 'image/svg+xml';
          break;
        case '.webp':
          mimeType = 'image/webp';
          break;
      }

      // 转换为base64 data URL
      const base64 = imageBuffer.toString('base64');
      const dataUrl = `data:${mimeType};base64,${base64}`;

      return { success: true, dataUrl };
    } catch (error) {
      console.error("获取图片数据失败:", error);
      return { success: false, error: error.message };
    }
  });

  // PDF文件处理器
  ipcMain.handle("read-pdf-file", async (event, pdfPath) => {
    try {
      const fs = require('fs');
      const path = require('path');

      if (!fs.existsSync(pdfPath)) {
        throw new Error('PDF文件不存在');
      }

      // 检查文件扩展名
      const ext = path.extname(pdfPath).toLowerCase();
      if (ext !== '.pdf') {
        throw new Error('不是有效的PDF文件');
      }

      // 读取PDF文件为ArrayBuffer
      const pdfBuffer = fs.readFileSync(pdfPath);

      // 将Buffer转换为ArrayBuffer
      const arrayBuffer = pdfBuffer.buffer.slice(
        pdfBuffer.byteOffset,
        pdfBuffer.byteOffset + pdfBuffer.byteLength
      );

      return { success: true, data: arrayBuffer };
    } catch (error) {
      console.error("读取PDF文件失败:", error);
      return { success: false, error: error.message };
    }
  });

  // ==================== 系统音频捕获相关处理器 ====================

  // 检查系统音频权限
  ipcMain.handle("check-system-audio-permission", async () => {
    try {
      return await SystemAudioCapture.checkPermissions();
    } catch (error) {
      console.error("检查系统音频权限失败:", error);
      return false;
    }
  });

  // 获取可用的音频源
  ipcMain.handle("get-system-audio-sources", async () => {
    try {
      return await SystemAudioCapture.getAudioSources();
    } catch (error) {
      console.error("获取系统音频源失败:", error);
      return [];
    }
  });

  // 预初始化系统音频捕获
  ipcMain.handle("pre-initialize-system-audio", async (event, options = {}) => {
    try {
      const mainWindow = deps.getMainWindow();
      if (!mainWindow) {
        console.warn("主窗口未找到，跳过预初始化");
        return { success: false, error: "主窗口未找到" };
      }

      // 初始化实例（如果还没有）
      const capture = initializeSystemAudioCapture(mainWindow, options);

      // 执行预初始化
      await capture.preInitialize();

      return { success: true };
    } catch (error) {
      console.error("预初始化系统音频捕获失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 初始化系统音频捕获实例（优化版本）
  const initializeSystemAudioCapture = (mainWindow: BrowserWindow, options: any = {}) => {
    if (systemAudioCapture) {
      // 更新主窗口引用
      systemAudioCapture.setMainWindow(mainWindow);
      return systemAudioCapture;
    }

    systemAudioCapture = new SystemAudioCapture(options, mainWindow);

    // 监听音频数据事件
    systemAudioCapture.on('audioData', (audioData) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('system-audio-data', audioData);
      }
    });

    // 监听错误事件
    systemAudioCapture.on('error', (error) => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('system-audio-error', error.message);
      }
    });

    // 监听开始事件
    systemAudioCapture.on('started', () => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('system-audio-started');
      }
    });

    // 监听停止事件
    systemAudioCapture.on('stopped', () => {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('system-audio-stopped');
      }
    });

    return systemAudioCapture;
  };

  // 开始系统音频捕获（优化版本）
  ipcMain.handle("start-system-audio-capture", async (event, options = {}) => {
    try {
      if (systemAudioCapture && systemAudioCapture.isActive()) {
        console.log("系统音频捕获已在运行");
        return { success: true };
      }

      const mainWindow = deps.getMainWindow();
      if (!mainWindow) {
        throw new Error("主窗口未找到");
      }

      // 初始化或复用实例
      const capture = initializeSystemAudioCapture(mainWindow, options);

      // 预初始化（如果还没有预初始化）
      await capture.preInitialize();

      const success = await capture.startCapture();
      return { success };
    } catch (error) {
      console.error("启动系统音频捕获失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 清除系统音频缓存
  ipcMain.handle("clear-system-audio-cache", async () => {
    try {
      console.log("清除系统音频缓存...");
      SystemAudioCapture.clearCache();
      return { success: true };
    } catch (error) {
      console.error("清除系统音频缓存失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 停止系统音频捕获
  ipcMain.handle("stop-system-audio-capture", async () => {
    try {
      if (systemAudioCapture) {
        await systemAudioCapture.stopCapture();
        systemAudioCapture = null;
      }
      return { success: true };
    } catch (error) {
      console.error("停止系统音频捕获失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 处理来自渲染进程的系统音频数据
  ipcMain.handle("handle-system-audio-data", async (event, audioData: ArrayBuffer) => {
    try {
      console.log(`IPC收到系统音频数据，大小: ${audioData.byteLength} 字节`);
      if (systemAudioCapture) {
        systemAudioCapture.handleAudioData(audioData);
      } else {
        console.warn("systemAudioCapture实例不存在");
      }
      return { success: true };
    } catch (error) {
      console.error("处理系统音频数据失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 检查系统音频捕获状态
  ipcMain.handle("is-system-audio-capturing", () => {
    return systemAudioCapture ? systemAudioCapture.isActive() : false;
  });

  // 获取系统音频捕获配置
  ipcMain.handle("get-system-audio-config", () => {
    return systemAudioCapture ? systemAudioCapture.getOptions() : null;
  });

  // ==================== 热词表管理相关处理器 ====================

  // 创建热词表
  ipcMain.handle("create-vocabulary-table", async (event, name: string, hotWords: any[]) => {
    try {
      const table = voiceConfigHelper.createVocabularyTable(name, hotWords);
      return { success: true, table };
    } catch (error) {
      console.error("创建热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 更新热词表
  ipcMain.handle("update-vocabulary-table", async (event, id: string, updates: any) => {
    try {
      const table = voiceConfigHelper.updateVocabularyTable(id, updates);
      if (table) {
        return { success: true, table };
      } else {
        return { success: false, error: "热词表不存在" };
      }
    } catch (error) {
      console.error("更新热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 删除热词表
  ipcMain.handle("delete-vocabulary-table", async (event, id: string) => {
    try {
      const success = voiceConfigHelper.deleteVocabularyTable(id);
      return { success };
    } catch (error) {
      console.error("删除热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 设置激活的热词表
  ipcMain.handle("set-active-vocabulary-table", async (event, id: string | undefined) => {
    try {
      const success = voiceConfigHelper.setActiveVocabularyTable(id);
      return { success };
    } catch (error) {
      console.error("设置激活热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 获取所有热词表
  ipcMain.handle("get-vocabulary-tables", async () => {
    try {
      const tables = voiceConfigHelper.getVocabularyTables();
      return { success: true, tables };
    } catch (error) {
      console.error("获取热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 获取激活的热词表
  ipcMain.handle("get-active-vocabulary-table", async () => {
    try {
      const table = voiceConfigHelper.getActiveVocabularyTable();
      return { success: true, table };
    } catch (error) {
      console.error("获取激活热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 同步热词表到阿里云
  ipcMain.handle("sync-vocabulary-to-cloud", async (event, tableId: string) => {
    try {
      const config = voiceConfigHelper.loadConfig();
      const table = config.vocabularyTables.find(t => t.id === tableId);

      if (!table) {
        console.error('热词表不存在, tableId:', tableId);
        return { success: false, error: "热词表不存在" };
      }

      const apiKey = config.tongyiGummy.apiKey;
      if (!apiKey) {
        console.error('API密钥未配置');
        return { success: false, error: "请先配置通义Gummy API密钥" };
      }

      const vocabularyApi = new VocabularyApiHelper(apiKey);

      // 转换热词格式
      const apiHotWords = table.hotWords.map(word => ({
        text: word.text,
        lang: word.lang
      }));

      let result;
      if (table.vocabularyId) {
        console.log('更新现有热词表, vocabularyId:', table.vocabularyId);
        // 更新现有热词表
        result = await vocabularyApi.updateVocabulary(table.vocabularyId, apiHotWords);
      } else {
        console.log('创建新热词表, prefix:', table.prefix);
        // 创建新热词表
        result = await vocabularyApi.createVocabulary(table.prefix, apiHotWords);

        if (result.success && result.vocabularyId) {
          console.log('保存热词表ID到本地:', result.vocabularyId);
          // 保存返回的热词表ID
          const updateResult = voiceConfigHelper.updateVocabularyTable(tableId, { vocabularyId: result.vocabularyId });
        }
      }

      console.log('最终同步结果:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error("同步热词表到云端失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

  // 从阿里云删除热词表
  ipcMain.handle("delete-vocabulary-from-cloud", async (event, tableId: string) => {
    try {
      const config = voiceConfigHelper.loadConfig();
      const table = config.vocabularyTables.find(t => t.id === tableId);

      if (!table || !table.vocabularyId) {
        return { success: false, error: "热词表不存在或未同步到云端" };
      }

      const apiKey = config.tongyiGummy.apiKey;
      if (!apiKey) {
        return { success: false, error: "请先配置通义Gummy API密钥" };
      }

      const vocabularyApi = new VocabularyApiHelper(apiKey);
      const result = await vocabularyApi.deleteVocabulary(table.vocabularyId);

      if (result.success) {
        // 清除本地的云端ID
        voiceConfigHelper.updateVocabularyTable(tableId, { vocabularyId: undefined });
      }

      return result;
    } catch (error) {
      console.error("从云端删除热词表失败:", error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  });

}

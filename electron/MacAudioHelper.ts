/**
 * Mac音频助手
 * 专门处理Mac平台的系统音频捕获问题
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface MacAudioDevice {
  id: string;
  name: string;
  type: 'input' | 'output';
  isVirtual: boolean;
}

export class MacAudioHelper {
  private static instance: MacAudioHelper | null = null;

  private constructor() {}

  static getInstance(): MacAudioHelper {
    if (!MacAudioHelper.instance) {
      MacAudioHelper.instance = new MacAudioHelper();
    }
    return MacAudioHelper.instance;
  }

  /**
   * 检测Mac系统是否安装了虚拟音频设备
   */
  async detectVirtualAudioDevices(): Promise<MacAudioDevice[]> {
    if (process.platform !== 'darwin') {
      return [];
    }

    try {
      console.log('检测Mac虚拟音频设备...');
      
      // 使用system_profiler获取音频设备信息
      const { stdout } = await execAsync('system_profiler SPAudioDataType -json');
      const audioData = JSON.parse(stdout);
      
      const virtualDevices: MacAudioDevice[] = [];
      
      // 解析音频设备数据
      if (audioData.SPAudioDataType) {
        for (const device of audioData.SPAudioDataType) {
          const deviceName = device._name || '';
          
          // 检测常见的虚拟音频设备
          const isVirtual = this.isVirtualAudioDevice(deviceName);
          
          if (isVirtual) {
            virtualDevices.push({
              id: device._name || '',
              name: deviceName,
              type: 'output', // 大多数虚拟设备都是输出设备
              isVirtual: true
            });
          }
        }
      }

      console.log(`检测到 ${virtualDevices.length} 个虚拟音频设备:`, virtualDevices.map(d => d.name));
      return virtualDevices;
    } catch (error) {
      console.warn('检测虚拟音频设备失败:', error);
      return [];
    }
  }

  /**
   * 判断是否为虚拟音频设备
   */
  private isVirtualAudioDevice(deviceName: string): boolean {
    const virtualDeviceNames = [
      'blackhole',
      'soundflower',
      'loopback',
      'virtual',
      'aggregate',
      'multi-output'
    ];

    const lowerName = deviceName.toLowerCase();
    return virtualDeviceNames.some(name => lowerName.includes(name));
  }

  /**
   * 检查Mac系统音频配置
   */
  async checkMacAudioConfiguration(): Promise<{
    hasVirtualDevice: boolean;
    currentOutputDevice: string;
    recommendations: string[];
  }> {
    if (process.platform !== 'darwin') {
      return {
        hasVirtualDevice: false,
        currentOutputDevice: '',
        recommendations: []
      };
    }

    try {
      const virtualDevices = await this.detectVirtualAudioDevices();
      const hasVirtualDevice = virtualDevices.length > 0;
      
      // 获取当前音频输出设备
      let currentOutputDevice = '';
      try {
        const { stdout } = await execAsync('defaults read com.apple.systemuiserver menuExtras | grep -o "Sound.menu"');
        if (stdout.includes('Sound.menu')) {
          // 可以进一步获取当前输出设备信息
          currentOutputDevice = '系统默认';
        }
      } catch (error) {
        console.warn('获取当前输出设备失败:', error);
      }

      const recommendations: string[] = [];
      
      if (!hasVirtualDevice) {
        recommendations.push('安装虚拟音频设备（推荐BlackHole）');
        recommendations.push('访问: https://github.com/ExistentialAudio/BlackHole');
      } else {
        recommendations.push('已检测到虚拟音频设备');
        recommendations.push('请确保系统音频输出设置为多输出设备');
        recommendations.push('在"音频MIDI设置"中配置多输出设备');
      }

      return {
        hasVirtualDevice,
        currentOutputDevice,
        recommendations
      };
    } catch (error) {
      console.error('检查Mac音频配置失败:', error);
      return {
        hasVirtualDevice: false,
        currentOutputDevice: '',
        recommendations: ['无法检测音频配置，请手动检查']
      };
    }
  }

  /**
   * 生成Mac系统音频设置指南
   */
  generateSetupGuide(): string[] {
    return [
      '=== Mac系统音频捕获设置指南 ===',
      '',
      '1. 安装BlackHole虚拟音频设备:',
      '   - 访问: https://github.com/ExistentialAudio/BlackHole',
      '   - 下载并安装BlackHole',
      '',
      '2. 配置多输出设备:',
      '   - 打开"应用程序 > 实用工具 > 音频MIDI设置"',
      '   - 点击左下角"+"按钮，选择"创建多输出设备"',
      '   - 勾选"内建输出"和"BlackHole 2ch"',
      '   - 将"内建输出"设为主设备',
      '',
      '3. 设置系统音频输出:',
      '   - 打开"系统偏好设置 > 声音 > 输出"',
      '   - 选择刚创建的"多输出设备"',
      '',
      '4. 重启应用:',
      '   - 完成配置后重启本应用',
      '   - 现在应该可以捕获系统音频了',
      '',
      '注意: 这是Mac系统的限制，需要虚拟音频设备才能捕获系统音频输出。'
    ];
  }

  /**
   * 打开Mac音频设置
   */
  async openAudioMIDISetup(): Promise<boolean> {
    if (process.platform !== 'darwin') {
      return false;
    }

    try {
      await execAsync('open "/Applications/Utilities/Audio MIDI Setup.app"');
      console.log('已打开音频MIDI设置');
      return true;
    } catch (error) {
      console.error('打开音频MIDI设置失败:', error);
      return false;
    }
  }

  /**
   * 打开系统声音偏好设置
   */
  async openSoundPreferences(): Promise<boolean> {
    if (process.platform !== 'darwin') {
      return false;
    }

    try {
      await execAsync('open "/System/Library/PreferencePanes/Sound.prefPane"');
      console.log('已打开系统声音偏好设置');
      return true;
    } catch (error) {
      console.error('打开系统声音偏好设置失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const macAudioHelper = MacAudioHelper.getInstance();

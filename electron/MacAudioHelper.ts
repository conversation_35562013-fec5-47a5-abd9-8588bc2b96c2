/**
 * Mac音频助手
 * 专门处理Mac平台的系统音频捕获问题
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface MacAudioDevice {
  id: string;
  name: string;
  type: 'input' | 'output';
  isVirtual: boolean;
}

export class MacAudioHelper {
  private static instance: MacAudioHelper | null = null;

  private constructor() {}

  static getInstance(): MacAudioHelper {
    if (!MacAudioHelper.instance) {
      MacAudioHelper.instance = new MacAudioHelper();
    }
    return MacAudioHelper.instance;
  }

  /**
   * 检测Mac系统是否安装了虚拟音频设备
   */
  async detectVirtualAudioDevices(): Promise<MacAudioDevice[]> {
    if (process.platform !== 'darwin') {
      return [];
    }

    try {
      console.log('检测Mac虚拟音频设备...');
      
      // 使用system_profiler获取音频设备信息
      const { stdout } = await execAsync('system_profiler SPAudioDataType -json');
      const audioData = JSON.parse(stdout);
      
      const virtualDevices: MacAudioDevice[] = [];
      
      // 解析音频设备数据
      if (audioData.SPAudioDataType) {
        for (const device of audioData.SPAudioDataType) {
          const deviceName = device._name || '';
          
          // 检测常见的虚拟音频设备
          const isVirtual = this.isVirtualAudioDevice(deviceName);
          
          if (isVirtual) {
            virtualDevices.push({
              id: device._name || '',
              name: deviceName,
              type: 'output', // 大多数虚拟设备都是输出设备
              isVirtual: true
            });
          }
        }
      }

      console.log(`检测到 ${virtualDevices.length} 个虚拟音频设备:`, virtualDevices.map(d => d.name));
      return virtualDevices;
    } catch (error) {
      console.warn('检测虚拟音频设备失败:', error);
      return [];
    }
  }

  /**
   * 判断是否为虚拟音频设备
   */
  private isVirtualAudioDevice(deviceName: string): boolean {
    const virtualDeviceNames = [
      'blackhole',
      'soundflower',
      'loopback',
      'virtual',
      'aggregate',
      'multi-output'
    ];

    const lowerName = deviceName.toLowerCase();
    return virtualDeviceNames.some(name => lowerName.includes(name));
  }

  /**
   * 检查Mac系统音频配置（增强BlackHole检测）
   */
  async checkMacAudioConfiguration(): Promise<{
    hasVirtualDevice: boolean;
    currentOutputDevice: string;
    recommendations: string[];
    isBlackHoleConfigured: boolean;
    systemAudioRouting: string;
  }> {
    if (process.platform !== 'darwin') {
      return {
        hasVirtualDevice: false,
        currentOutputDevice: '',
        recommendations: [],
        isBlackHoleConfigured: false,
        systemAudioRouting: ''
      };
    }

    try {
      const virtualDevices = await this.detectVirtualAudioDevices();
      const hasVirtualDevice = virtualDevices.length > 0;

      // 检查BlackHole是否正确配置
      const isBlackHoleConfigured = await this.checkBlackHoleConfiguration();

      // 获取当前音频输出设备和路由信息
      const { currentOutputDevice, systemAudioRouting } = await this.getCurrentAudioConfiguration();

      const recommendations: string[] = [];

      if (!hasVirtualDevice) {
        recommendations.push('❌ 未检测到虚拟音频设备');
        recommendations.push('📥 安装BlackHole: https://github.com/ExistentialAudio/BlackHole');
      } else {
        recommendations.push('✅ 已检测到虚拟音频设备');

        if (!isBlackHoleConfigured) {
          recommendations.push('⚠️ BlackHole未正确配置');
          recommendations.push('🔧 请在"音频MIDI设置"中创建多输出设备');
          recommendations.push('🔊 将系统音频输出设置为多输出设备');
        } else {
          recommendations.push('✅ BlackHole配置正确');
          recommendations.push('🎵 系统音频应该可以被捕获');
        }
      }

      // 检查系统音频路由
      if (systemAudioRouting && !systemAudioRouting.toLowerCase().includes('multi') &&
          !systemAudioRouting.toLowerCase().includes('aggregate')) {
        recommendations.push('⚠️ 系统音频输出未设置为多输出设备');
        recommendations.push('🔧 请在"系统偏好设置 > 声音"中选择多输出设备');
      }

      return {
        hasVirtualDevice,
        currentOutputDevice,
        recommendations,
        isBlackHoleConfigured,
        systemAudioRouting
      };
    } catch (error) {
      console.error('检查Mac音频配置失败:', error);
      return {
        hasVirtualDevice: false,
        currentOutputDevice: '',
        recommendations: ['❌ 无法检测音频配置，请手动检查'],
        isBlackHoleConfigured: false,
        systemAudioRouting: ''
      };
    }
  }

  /**
   * 检查BlackHole是否正确配置
   */
  private async checkBlackHoleConfiguration(): Promise<boolean> {
    try {
      // 检查BlackHole进程是否运行
      const { stdout } = await execAsync('ps aux | grep -i blackhole | grep -v grep');
      const hasBlackHoleProcess = stdout.trim().length > 0;

      // 检查音频设备中是否有BlackHole
      const audioDevicesOutput = await execAsync('system_profiler SPAudioDataType | grep -i blackhole');
      const hasBlackHoleDevice = audioDevicesOutput.stdout.trim().length > 0;

      console.log('BlackHole配置检查:');
      console.log('- BlackHole进程运行:', hasBlackHoleProcess);
      console.log('- BlackHole设备存在:', hasBlackHoleDevice);

      return hasBlackHoleDevice; // 主要看设备是否存在
    } catch (error) {
      console.warn('检查BlackHole配置失败:', error);
      return false;
    }
  }

  /**
   * 获取当前音频配置信息
   */
  private async getCurrentAudioConfiguration(): Promise<{
    currentOutputDevice: string;
    systemAudioRouting: string;
  }> {
    try {
      // 获取当前音频输出设备
      let currentOutputDevice = '';
      let systemAudioRouting = '';

      try {
        // 使用AppleScript获取当前音频输出设备
        const { stdout } = await execAsync(`osascript -e "
          tell application \\"System Events\\"
            tell process \\"System Preferences\\"
              -- 这里可以添加更多检测逻辑
            end tell
          end tell
        "`);

        // 简化版本：直接检查音频设备
        const audioOutput = await execAsync('system_profiler SPAudioDataType | grep -A 5 "Default Output Device"');
        currentOutputDevice = audioOutput.stdout.trim() || '未知';
        systemAudioRouting = currentOutputDevice;

      } catch (error) {
        console.warn('获取音频配置详情失败:', error);
        currentOutputDevice = '检测失败';
        systemAudioRouting = '检测失败';
      }

      return {
        currentOutputDevice,
        systemAudioRouting
      };
    } catch (error) {
      console.error('获取当前音频配置失败:', error);
      return {
        currentOutputDevice: '未知',
        systemAudioRouting: '未知'
      };
    }
  }

  /**
   * 生成Mac系统音频设置指南
   */
  generateSetupGuide(): string[] {
    return [
      '=== Mac系统音频捕获设置指南 ===',
      '',
      '1. 安装BlackHole虚拟音频设备:',
      '   - 访问: https://github.com/ExistentialAudio/BlackHole',
      '   - 下载并安装BlackHole',
      '',
      '2. 配置多输出设备:',
      '   - 打开"应用程序 > 实用工具 > 音频MIDI设置"',
      '   - 点击左下角"+"按钮，选择"创建多输出设备"',
      '   - 勾选"内建输出"和"BlackHole 2ch"',
      '   - 将"内建输出"设为主设备',
      '',
      '3. 设置系统音频输出:',
      '   - 打开"系统偏好设置 > 声音 > 输出"',
      '   - 选择刚创建的"多输出设备"',
      '',
      '4. 重启应用:',
      '   - 完成配置后重启本应用',
      '   - 现在应该可以捕获系统音频了',
      '',
      '注意: 这是Mac系统的限制，需要虚拟音频设备才能捕获系统音频输出。'
    ];
  }

  /**
   * 打开Mac音频设置
   */
  async openAudioMIDISetup(): Promise<boolean> {
    if (process.platform !== 'darwin') {
      return false;
    }

    try {
      await execAsync('open "/Applications/Utilities/Audio MIDI Setup.app"');
      console.log('已打开音频MIDI设置');
      return true;
    } catch (error) {
      console.error('打开音频MIDI设置失败:', error);
      return false;
    }
  }

  /**
   * 打开系统声音偏好设置
   */
  async openSoundPreferences(): Promise<boolean> {
    if (process.platform !== 'darwin') {
      return false;
    }

    try {
      await execAsync('open "/System/Library/PreferencePanes/Sound.prefPane"');
      console.log('已打开系统声音偏好设置');
      return true;
    } catch (error) {
      console.error('打开系统声音偏好设置失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const macAudioHelper = MacAudioHelper.getInstance();
